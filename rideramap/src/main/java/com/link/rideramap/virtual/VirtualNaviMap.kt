package com.link.rideramap.virtual

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import com.amap.api.maps.AMap
import com.amap.api.maps.CustomRenderer
import com.amap.api.navi.AMapNaviView
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class VirtualNaviMap @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AMapNaviView(context, attrs) {
    private lateinit var mAMap: AMap
    private var mActionListener: IActionListener? = null


    private val customRender = object : CustomRenderer {
        override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
            mAMap.setCustomRenderer(null)
            post {
                mActionListener?.onVisible()
            }
        }

        override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {

        }

        override fun onDrawFrame(gl: GL10?) {

        }

        override fun OnMapReferencechanged() {

        }

    }

    fun create(bundle: Bundle?) {
        super.onCreate(bundle)
        mAMap = map
        mAMap.setCustomRenderer(customRender)
    }

    fun destroy() {
        super.onDestroy()
        mAMap.setCustomRenderer(null)
    }

    fun setActionListener(iActionListener: IActionListener) {
        mActionListener = iActionListener
    }

    companion object {
        private const val TAG = "VirtualNaviMap"
    }
}