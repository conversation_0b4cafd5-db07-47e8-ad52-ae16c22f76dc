package com.link.rideramap.virtual

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.View
import android.widget.FrameLayout
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.common.base.IVirtualHomeView
import com.link.rideramap.map.NaviCallback
import com.link.rideramap.utils.AutoSizeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 虚拟主页视图
 * 重构后作为纯容器，不再实现IMapParent接口
 * 职责仅限于视图容器和控制器的生命周期管理
 * 
 * <AUTHOR>
 * @date 2023/5/4
 */
class VirtualHomeView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : IVirtualHomeView(context, attrs, defStyleAttr) {

    // 容器视图
    private var mapContainer: FrameLayout? = null
    private var naviContainer: FrameLayout? = null
    private var shadowView: View? = null

    // 地图组件
    private var virtualMap: VirtualMap? = null
    private var aMap: AMap? = null
    private var marker: Marker? = null
    private var locationJob: Job? = null

    // 导航组件
    private var virtualNaviMap: VirtualNaviMap? = null
    private var virtualNaviView: VirtualNaviView? = null

    // 状态管理
    private var currentContentType = VirtualContentType.MAP
    private var isMapInitialized = false
    private var isNaviInitialized = false
    private var isMapDestroyed = false
    private var isNaviMapDestroyed = false

    private var savedInstanceState: Bundle? = null
    private lateinit var display: Display

    private val naviDataCallback = object : NaviCallback() {
        override fun onStartNavi() {
            super.onStartNavi()
            Log.d(TAG, "onStartNavi")
            post { switchToNavigation() }
        }

        override fun onStopNavi() {
            super.onStopNavi()
            Log.d(TAG, "onStopNavi")
            post { switchToMap() }
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            Log.d(TAG, "onEndEmulatorNavi")
            post { switchToMap() }
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            Log.d(TAG, "onArriveDestination")
            post { switchToMap() }
        }

        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            virtualNaviView?.onNaviDataChanged(navigationInfo)
        }

        override fun onGpsSignalWeak(isWeek: Boolean) {
            super.onGpsSignalWeak(isWeek)
            virtualNaviView?.onGpsSignalWeak(isWeek)
        }
    }


    override fun onCreate(bundle: Bundle?, display: Display) {
        val startTime = System.currentTimeMillis()
        this.savedInstanceState = bundle
        this.display = display

        Log.d(TAG, "VirtualHomeView onCreate start at: $startTime")

        try {
            // 初始化容器视图
            initializeContainers()

            // 初始化阴影视图
            initializeShadowView()

            // 根据当前状态决定优先初始化哪个内容
            val isNavi = RiderMap.instance.isNavi()
            currentContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP

            if (isNavi) {
                // 导航模式：优先初始化导航内容
                initializeNaviContent(bundle, display)
                // 延迟初始化地图内容
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeMapContent(bundle, display)
                }, 50)
            } else {
                // 普通模式：优先初始化地图内容
                initializeMapContent(bundle, display)
                // 延迟初始化导航内容
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeNaviContent(bundle, display)
                }, 50)
            }

            // 设置初始可见性
            updateContentVisibility()

            // 注册导航状态监听
            RiderMap.instance.addNaviDataCallback(naviDataCallback)

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualHomeView created in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to create VirtualHomeView", e)
            throw e
        }
    }

    /**
     * 初始化容器视图
     */
    private fun initializeContainers() {
        mapContainer = findViewById(R.id.map_container)
        naviContainer = findViewById(R.id.navi_container)
        shadowView = findViewById(R.id.v_map_shadow)
    }

    /**
     * 初始化阴影视图
     */
    private fun initializeShadowView() {
        shadowView?.setBackgroundColor(android.graphics.Color.argb(0xFF, 0XEB, 0XEB, 0XEB))
    }

    /**
     * 初始化地图内容
     */
    private fun initializeMapContent(bundle: Bundle?, display: Display) {
        if (isMapInitialized) {
            Log.d(TAG, "Map content already initialized")
            return
        }

        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Initializing map content at: $startTime")

            // 初始化虚拟地图
            virtualMap = findViewById(R.id.virtual_map)
            aMap = virtualMap?.map?.apply {
                uiSettings.isZoomControlsEnabled = false
                uiSettings.isIndoorSwitchEnabled = false
                uiSettings.isZoomGesturesEnabled = false
                showBuildings(false)
                setRenderFps(15)
            }

            // 设置地图类型
            aMap?.mapType = RiderMap.instance.getDefaultMode()

            // 设置地图可见监听器
            virtualMap?.setActionListener(object : IActionListener {
                override fun onVisible() {
                    shadowView?.visibility = View.GONE
                }
            })

            // 创建地图
            virtualMap?.create(bundle)

            // 设置当前位置
            setupLocation()

            isMapInitialized = true
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Map content initialized in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize map content", e)
        }
    }

    /**
     * 初始化导航内容
     */
    private fun initializeNaviContent(bundle: Bundle?, display: Display) {
        if (isNaviInitialized) {
            Log.d(TAG, "Navigation content already initialized")
            return
        }

        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Initializing navigation content at: $startTime")

            // 初始化导航地图
            virtualNaviMap = findViewById(R.id.navi_map)
            virtualNaviMap?.create(bundle)

            // 设置地图可见监听器
            virtualNaviMap?.setActionListener(object : IActionListener {
                override fun onVisible() {
                    shadowView?.visibility = View.GONE
                    setupNaviViewOptions()
                }
            })

            // 添加导航UI视图
            addVirtualNaviView(display)

            isNaviInitialized = true
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Navigation content initialized in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize navigation content", e)
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy called")
        cleanupResources()
        Log.d(TAG, "VirtualHomeView destroyed")
    }

    /**
     * 根据屏幕方向添加对应的导航视图
     */
    private fun addVirtualNaviView(display: Display) {
        val displayMetrics = DisplayMetrics()
        display.getMetrics(displayMetrics)

        virtualNaviView = if (displayMetrics.widthPixels < displayMetrics.heightPixels) {
            VirtualNaviPortView(context) // 竖屏
        } else {
            VirtualNaviLandView(context) // 横屏
        }

        findViewById<FrameLayout>(R.id.navi_ui_container)?.addView(virtualNaviView)
    }

    /**
     * 设置导航视图选项
     */
    private fun setupNaviViewOptions() {
        val mapType = RiderMap.instance.getDefaultModeNavi()
        val viewOptions = virtualNaviMap?.viewOptions?.apply {
            isSettingMenuEnabled = false
            isLayoutVisible = false
            isTrafficBarEnabled = false
            isAutoChangeZoom = false
            setModeCrossDisplayShow(false)
            isRealCrossDisplayShow = false
            isAfterRouteAutoGray = true
            isAutoLockCar = true
            isDrawBackUpOverlay = false
            lockMapDelayed = 7000L
            tilt = 35
            carBitmap = BitmapFactory.decodeResource(resources, R.drawable.amap_navmylocation)
            isAutoNaviViewNightMode = false
            isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
        }
        virtualNaviMap?.viewOptions = viewOptions
    }

    /**
     * 设置位置信息
     */
    private fun setupLocation() {
        // 先尝试使用缓存的位置
        val cachedLocation = RiderMap.instance.getLocation()
        cachedLocation?.let {
            setPosition(it.latitude, it.longitude)
        }

        // 异步获取最新位置
        locationJob = MainScope().launch(Dispatchers.IO) {
            try {
                val locationInfo = RiderMap.instance.getLbsLocation()
                setPosition(locationInfo.latitude, locationInfo.longitude)
                RiderMap.instance.setLocation(locationInfo)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get location", e)
            }
        }
    }

    /**
     * 设置地图位置
     */
    private fun setPosition(latitude: Double, longitude: Double) {
        if (!isMapInitialized || aMap == null) return

        val latLng = LatLng(latitude, longitude)
        aMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 17f))
        updateMarker(latLng)
    }

    /**
     * 更新位置标记
     */
    private fun updateMarker(latLng: LatLng) {
        marker?.remove()

        val markerOptions = MarkerOptions().apply {
            anchor(0.5f, 0.5f)
            isFlat = true
            visible(true)
            zIndex(2.2f)
            position(latLng)

            // 设置标记图标
            val locationBitmap = BitmapFactory.decodeResource(resources, R.drawable.nearme)
            icon(BitmapDescriptorFactory.fromBitmap(locationBitmap))
        }

        marker = aMap?.addMarker(markerOptions)
    }

    /**
     * 切换到地图模式
     */
    private fun switchToMap() {
        if (currentContentType == VirtualContentType.MAP) {
            Log.d(TAG, "Already in map mode")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching to map mode at: $startTime")

        currentContentType = VirtualContentType.MAP
        updateContentVisibility()

        // 恢复地图
        if (isMapInitialized) {
            virtualMap?.onResume()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Switched to map mode in ${endTime - startTime}ms")
    }

    /**
     * 切换到导航模式
     */
    private fun switchToNavigation() {
        if (currentContentType == VirtualContentType.NAVIGATION) {
            Log.d(TAG, "Already in navigation mode")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching to navigation mode at: $startTime")

        currentContentType = VirtualContentType.NAVIGATION
        updateContentVisibility()

        // 恢复导航地图
        if (isNaviInitialized) {
            virtualNaviMap?.onResume()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Switched to navigation mode in ${endTime - startTime}ms")
    }

    /**
     * 更新内容可见性
     */
    private fun updateContentVisibility() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                mapContainer?.visibility = View.VISIBLE
                naviContainer?.visibility = View.GONE
            }
            VirtualContentType.NAVIGATION -> {
                mapContainer?.visibility = View.GONE
                naviContainer?.visibility = View.VISIBLE
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.d(TAG, "onDetachedFromWindow - auto cleanup")

        // 在View被移除时自动清理，让地图资源在View生命周期结束时销毁
        cleanupResources()
    }

    private fun cleanupResources() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting VirtualHomeView cleanup at: $startTime")

            // 移除导航状态监听，防止内存泄漏
            val callbackStartTime = System.currentTimeMillis()
            RiderMap.instance.removeNaviDataCallback(naviDataCallback)
            Log.d(TAG, "Removed navi callback in ${System.currentTimeMillis() - callbackStartTime}ms")

            // 立即清理关键资源
            locationJob?.cancel()
            marker?.remove()
            virtualNaviView = null

            // 异步销毁地图资源
            if (!isMapDestroyed && isMapInitialized) {
                isMapDestroyed = true
                Handler(Looper.getMainLooper()).post {
                    val mapDestroyStartTime = System.currentTimeMillis()
                    try {
                        virtualMap?.destroy()
                        Log.d(TAG, "VirtualMap destroyed in ${System.currentTimeMillis() - mapDestroyStartTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual map", e)
                    }
                }
            }

            // 异步销毁导航地图资源
            if (!isNaviMapDestroyed && isNaviInitialized) {
                isNaviMapDestroyed = true
                Handler(Looper.getMainLooper()).post {
                    val naviDestroyStartTime = System.currentTimeMillis()
                    try {
                        virtualNaviMap?.destroy()
                        Log.d(TAG, "VirtualNaviMap destroyed in ${System.currentTimeMillis() - naviDestroyStartTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual navi map", e)
                    }
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualHomeView resources cleaned up in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup VirtualHomeView resources", e)
        }
    }

    override fun onNavigationMapFragmentCreate() {
        // 导航地图片段创建时的回调
    }

    override fun onResume() {
        post {
            mapController?.resumeContent()
        }
    }

    override fun onStart() {
        // 启动时的处理
    }

    override fun onStop() {
        // 停止时的处理
    }

    override fun changeMap(type: Int) {
        changeMapType(type)
    }

    /**
     * 改变地图类型
     * @param type 地图类型
     */
    fun changeMapType(type: Int) {
        mapController?.changeMapType(type)
        Log.d(TAG, "changeMapType: $type")
    }

    /**
     * 改变主题
     * @param themeType 主题类型
     */
    fun changeTheme(themeType: Int) {
        mapController?.changeTheme(themeType)
        Log.d(TAG, "changeTheme: $themeType")
    }

    init {
        // 初始化UI和自适应布局
        AutoSizeUtils.updateResources(context)
        inflate(context, R.layout.view_virtual_home, this)
        containerView = findViewById(R.id.virtual_container)
    }

    companion object {
        private const val TAG = "VirtualHomeView"
    }
}