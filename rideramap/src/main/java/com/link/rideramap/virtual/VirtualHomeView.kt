package com.link.rideramap.virtual

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.util.Log
import android.view.Display
import android.widget.FrameLayout
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.common.base.IVirtualHomeView
import com.link.rideramap.map.NaviCallback
import com.link.rideramap.utils.AutoSizeUtils

/**
 * 虚拟主页视图
 * 重构后作为纯容器，不再实现IMapParent接口
 * 职责仅限于视图容器和控制器的生命周期管理
 * 
 * <AUTHOR>
 * @date 2023/5/4
 */
class VirtualHomeView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : IVirtualHomeView(context, attrs, defStyleAttr) {
    
    private var containerView: FrameLayout? = null
    private var mapController: IVirtualMapController? = null
    private var savedInstanceState: Bundle? = null
    private lateinit var display: Display

    private val naviDataCallback = object : NaviCallback() {
        override fun onStartNavi() {
            super.onStartNavi()
            Log.d(TAG, "onStartNavi")
            post { mapController?.refreshContent() }
        }

        override fun onStopNavi() {
            super.onStopNavi()
            Log.d(TAG, "onStopNavi")
            post { mapController?.refreshContent() }
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            Log.d(TAG, "onEndEmulatorNavi")
            post { mapController?.refreshContent() }
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            Log.d(TAG, "onArriveDestination")
            post { mapController?.refreshContent() }
        }
    }


    override fun onCreate(bundle: Bundle?, display: Display) {
        this.savedInstanceState = bundle
        this.display = display
        
        // 初始化控制器
        containerView?.let { container ->
            mapController = VirtualMapController(container)
            mapController?.initialize(context, bundle, display)
        }
        
        // 注册导航状态监听
        RiderMap.instance.addNaviDataCallback(naviDataCallback)
        
        Log.d(TAG, "VirtualHomeView created")
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy called")
        cleanupResources()
        Log.d(TAG, "VirtualHomeView destroyed")
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.d(TAG, "onDetachedFromWindow - auto cleanup")

        // 在View被移除时自动清理，让地图资源在View生命周期结束时销毁
        cleanupResources()
    }

    private fun cleanupResources() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting VirtualHomeView cleanup at: $startTime")

            // 移除导航状态监听，防止内存泄漏
            val callbackStartTime = System.currentTimeMillis()
            RiderMap.instance.removeNaviDataCallback(naviDataCallback)
            Log.d(TAG, "Removed navi callback in ${System.currentTimeMillis() - callbackStartTime}ms")

            // 轻量级清理：只清空引用，让地图资源随View自然销毁
            val controllerStartTime = System.currentTimeMillis()
            mapController?.cleanup()
            mapController = null
            Log.d(TAG, "Controller cleanup in ${System.currentTimeMillis() - controllerStartTime}ms")

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualHomeView resources cleaned up (lightweight) in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup VirtualHomeView resources", e)
        }
    }

    override fun onNavigationMapFragmentCreate() {
        // 导航地图片段创建时的回调
    }

    override fun onResume() {
        post {
            mapController?.resumeContent()
        }
    }

    override fun onStart() {
        // 启动时的处理
    }

    override fun onStop() {
        // 停止时的处理
    }

    override fun changeMap(type: Int) {
        changeMapType(type)
    }

    /**
     * 改变地图类型
     * @param type 地图类型
     */
    fun changeMapType(type: Int) {
        mapController?.changeMapType(type)
        Log.d(TAG, "changeMapType: $type")
    }

    /**
     * 改变主题
     * @param themeType 主题类型
     */
    fun changeTheme(themeType: Int) {
        mapController?.changeTheme(themeType)
        Log.d(TAG, "changeTheme: $themeType")
    }

    init {
        // 初始化UI和自适应布局
        AutoSizeUtils.updateResources(context)
        inflate(context, R.layout.view_virtual_home, this)
        containerView = findViewById(R.id.virtual_container)
    }

    companion object {
        private const val TAG = "VirtualHomeView"
    }
}