package com.link.rideramap.virtual

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.View
import android.widget.FrameLayout
import com.amap.api.maps.AMap
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.map.NaviCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 导航内容提供者
 * 负责导航地图的显示和导航数据处理
 *
 * <AUTHOR>
 * @date 2023/5/4
 */
class NaviContentProvider(context: Context) : BaseVirtualContent(
    context,
    R.layout.virtual_navi_view,
    VirtualContentType.NAVIGATION
) {
    private lateinit var virtualNaviMap: VirtualNaviMap
    private var virtualNaviView: VirtualNaviView? = null
    private var isNaviMapDestroyed = false // 标记导航地图是否已被销毁

    private val naviDataCallback = object : NaviCallback() {
        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            virtualNaviView?.onNaviDataChanged(navigationInfo)
        }

        override fun onGpsSignalWeak(isWeek: Boolean) {
            super.onGpsSignalWeak(isWeek)
            virtualNaviView?.onGpsSignalWeak(isWeek)
        }
    }

    override fun provideShadowView(): View {
        return findViewById(R.id.v_map_shadow)
    }

    override fun onCreateContent(bundle: Bundle?, display: Display) {
        Log.d(TAG, "onCreate")

        // 初始化导航地图
        virtualNaviMap = findViewById(R.id.navi_map)
        virtualNaviMap.create(bundle)

        // 设置地图可见监听器
        virtualNaviMap.setActionListener(object : IActionListener {
            override fun onVisible() {
                hideShadowView()
                Log.d(TAG, "onVisible")
                setupViewOptions()
            }
        })

        // 添加导航UI视图
        addVirtualNaviView(display)

        // 注册导航数据回调
        RiderMap.instance.addNaviDataCallback(naviDataCallback)
    }

    /**
     * 根据屏幕方向添加对应的导航视图
     */
    private fun addVirtualNaviView(display: Display) {
        val displayMetrics = DisplayMetrics()
        display.getMetrics(displayMetrics)

        virtualNaviView = if (displayMetrics.widthPixels < displayMetrics.heightPixels) {
            VirtualNaviPortView(context) // 竖屏
        } else {
            VirtualNaviLandView(context) // 横屏
        }

        findViewById<FrameLayout>(R.id.virtual_root).addView(virtualNaviView)
    }

    /**
     * 设置导航视图选项
     */
    private fun setupViewOptions() {
        val mapType = RiderMap.instance.getDefaultModeNavi()
        val viewOptions = virtualNaviMap.viewOptions.apply {
            isSettingMenuEnabled = false
            isLayoutVisible = false
            isTrafficBarEnabled = false
            isAutoChangeZoom = false
            setModeCrossDisplayShow(false)
            isRealCrossDisplayShow = false
            isAfterRouteAutoGray = true
            isAutoLockCar = true
            isDrawBackUpOverlay = false
            lockMapDelayed = LOCK_MAP_DELAY
            tilt = MAP_TILT_ANGLE
            carBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.amap_navmylocation)
            isAutoNaviViewNightMode = false
            isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
        }
        virtualNaviMap.viewOptions = viewOptions
    }

    override fun onResumeContent() {
        Log.d(TAG, "onResume")
        virtualNaviMap.onResume()
    }

    override fun onCleanupContent() {
        Log.d(TAG, "onCleanupContent")
        // 轻量级清理：只处理关键资源，延迟执行地图销毁操作
        try {
            // 移除导航数据回调，避免内存泄漏
            RiderMap.instance.removeNaviDataCallback(naviDataCallback)
            
            // 清空导航视图引用
            virtualNaviView = null
            
            // 使用主线程延迟执行导航地图销毁，避免阻塞当前调用
            if (!isNaviMapDestroyed && ::virtualNaviMap.isInitialized) {
                isNaviMapDestroyed = true
                // 延迟到下一个消息循环执行，避免阻塞dismiss
                virtualNaviMap.post {
                    try {
                        virtualNaviMap.destroy()
                        Log.d(TAG, "VirtualNaviMap destroyed on main thread")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual navi map", e)
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup navigation content", e)
        }
    }

    override fun onDestroyContent() {
        Log.d(TAG, "onDestroy")
        // 移除导航数据回调
        RiderMap.instance.removeNaviDataCallback(naviDataCallback)

        // 检查是否已被异步销毁，避免重复销毁
        if (!isNaviMapDestroyed && ::virtualNaviMap.isInitialized) {
            isNaviMapDestroyed = true
            virtualNaviMap.destroy()
        }
    }

    override fun onChangeMapType(mapType: Int) {
        Log.d(TAG, "changeMapType: $mapType")
        val options = virtualNaviMap.viewOptions.apply {
            isAutoNaviViewNightMode = false
            isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
        }
        virtualNaviMap.viewOptions = options
    }

    override fun onChangeTheme(themeType: Int) {
        // TODO: 实现主题切换逻辑
    }

    companion object {
        private const val TAG = "NaviContentProvider"

        // 常量定义
        private const val LOCK_MAP_DELAY = 7000L
        private const val MAP_TILT_ANGLE = 35
    }
}