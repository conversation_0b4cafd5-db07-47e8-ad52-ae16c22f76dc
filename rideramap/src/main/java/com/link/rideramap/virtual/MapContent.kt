package com.link.rideramap.virtual

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Display
import android.view.View
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 地图内容提供者
 * 负责普通地图的显示和交互
 * 
 * <AUTHOR>  
 * @date 2023/5/4
 */
class MapContentProvider(context: Context) : BaseVirtualContent(
    context,
    R.layout.virtual_map_view,
    VirtualContentType.MAP
) {
    private lateinit var virtualMap: VirtualMap
    private lateinit var aMap: AMap
    private var marker: Marker? = null
    private var locationJob: Job? = null
    private var locationBitmap: Bitmap? = null

    // 标记地图是否已被销毁，防止重复销毁
    private var isMapDestroyed = false

    override fun provideShadowView(): View {
        return findViewById(R.id.v_map_shadow)
    }

    override fun onCreateContent(bundle: Bundle?, display: Display) {
        // 初始化虚拟地图
        virtualMap = findViewById(R.id.virtual_map)
        aMap = virtualMap.map.apply {
            uiSettings.isZoomControlsEnabled = false
            uiSettings.isIndoorSwitchEnabled = false
            uiSettings.isZoomGesturesEnabled = false
            showBuildings(false)
            setRenderFps(15)
        }
        
        // 设置地图类型
        aMap.mapType = RiderMap.instance.getDefaultMode()
        
        // 设置地图可见监听器
        virtualMap.setActionListener(object : IActionListener {
            override fun onVisible() {
                hideShadowView()
            }
        })
        
        // 创建地图
        virtualMap.create(bundle)
        
        // 设置当前位置
        setupLocation()
    }


    /**
     * 设置位置信息
     */
    private fun setupLocation() {
        // 先尝试使用缓存的位置
        val cachedLocation = RiderMap.instance.getLocation()
        cachedLocation?.let {
            setPosition(it.latitude, it.longitude)
        }
        
        // 异步获取最新位置
        locationJob = MainScope().launch(Dispatchers.IO) {
            try {
                val locationInfo = RiderMap.instance.getLbsLocation()
                setPosition(locationInfo.latitude, locationInfo.longitude)
                RiderMap.instance.setLocation(locationInfo)
            } catch (e: Exception) {
                // 位置获取失败，使用默认位置或显示错误
            }
        }
    }

    /**
     * 设置地图位置
     */
    private fun setPosition(latitude: Double, longitude: Double) {
        val latLng = LatLng(latitude, longitude)
        aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 17f))
        updateMarker(latLng)
    }

    /**
     * 更新位置标记
     */
    private fun updateMarker(latLng: LatLng) {
        marker?.remove()
        
        val markerOptions = MarkerOptions().apply {
            anchor(0.5f, 0.5f)
            isFlat = true
            visible(true)
            zIndex(2.2f)
            position(latLng)
            
            // 设置标记图标
            locationBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.nearme)
            icon(BitmapDescriptorFactory.fromBitmap(locationBitmap))
        }
        
        marker = aMap.addMarker(markerOptions)
    }

    override fun onResumeContent() {
        virtualMap.onResume()
    }

    override fun onCleanupContent() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "onCleanupContent start at: $startTime")

        // 轻量级清理：立即清理关键资源，延迟执行地图销毁
        try {
            // 立即取消位置任务和移除标记
            val jobStartTime = System.currentTimeMillis()
            locationJob?.cancel()
            marker?.remove()
            Log.d(TAG, "Job and marker cleanup in ${System.currentTimeMillis() - jobStartTime}ms")

            // 延迟执行地图销毁，避免阻塞当前调用
            if (!isMapDestroyed && ::virtualMap.isInitialized) {
                isMapDestroyed = true
                virtualMap.post {
                    val mapDestroyStartTime = System.currentTimeMillis()
                    try {
                        // 再次检查是否已被销毁，防止重复销毁
                        if (::virtualMap.isInitialized) {
                            virtualMap.destroy()
                            val mapDestroyEndTime = System.currentTimeMillis()
                            Log.d(TAG, "VirtualMap destroyed on main thread in ${mapDestroyEndTime - mapDestroyStartTime}ms")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual map", e)
                    }
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "onCleanupContent completed in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup map content", e)
        }
    }

    override fun onDestroyContent() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "onDestroyContent start at: $startTime")

        try {
            locationJob?.cancel()
            marker?.remove()

            // 同步销毁地图（用于直接调用destroy的情况）
            if (!isMapDestroyed && ::virtualMap.isInitialized) {
                isMapDestroyed = true
                virtualMap.destroy()
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "onDestroyContent completed in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy map content", e)
        }
    }

    override fun onChangeMapType(mapType: Int) {
        aMap.mapType = mapType
    }

    override fun onChangeTheme(themeType: Int) {
        // TODO: 实现主题切换逻辑
    }

    companion object {
        private const val TAG = "MapContentProvider"
    }
}