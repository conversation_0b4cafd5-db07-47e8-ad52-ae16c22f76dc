package com.link.rideramap.virtual

import android.content.Context

/**
 * 虚拟内容工厂
 * 负责创建不同类型的虚拟地图内容
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
object VirtualContentFactory {
    
    /**
     * 创建虚拟内容提供者
     * @param contentType 内容类型
     * @param context 上下文
     * @param useUnified 是否使用统一内容提供者
     * @return 对应的内容提供者
     */
    fun createContentProvider(
        contentType: VirtualContentType,
        context: Context,
        useUnified: Boolean = true
    ): IVirtualContentProvider {
        return if (useUnified) {
            // 使用统一内容提供者，避免重复创建
            UnifiedVirtualContentProvider(context)
        } else {
            // 使用传统的分离式内容提供者
            when (contentType) {
                VirtualContentType.MAP -> MapContentProvider(context)
                VirtualContentType.NAVIGATION -> NaviContentProvider(context)
            }
        }
    }
    
    /**
     * 根据当前导航状态判断应该创建哪种内容
     * @param context 上下文
     * @return 对应的内容提供者
     */
    fun createContentProviderByNaviState(context: Context): IVirtualContentProvider {
        // 这里可以注入RiderMap的状态判断逻辑
        // 为了保持当前代码的兼容性，暂时保留对RiderMap的引用
        val isNavi = com.link.rideramap.api.RiderMap.instance.isNavi()
        
        return if (isNavi) {
            createContentProvider(VirtualContentType.NAVIGATION, context)
        } else {
            createContentProvider(VirtualContentType.MAP, context)
        }
    }
} 