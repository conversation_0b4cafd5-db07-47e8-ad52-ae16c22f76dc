package com.link.rideramap.virtual

import android.content.Context
import android.os.Bundle
import android.view.Display

/**
 * 虚拟地图控制器接口
 * 负责管理虚拟地图内容的切换和生命周期
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
interface IVirtualMapController {
    
    /**
     * 当前活动的内容提供者
     */
    val currentContentProvider: IVirtualContentProvider?
    
    /**
     * 初始化控制器
     * @param context 上下文
     * @param bundle 保存的状态数据
     * @param display 显示器信息
     */
    fun initialize(context: Context, bundle: Bundle?, display: Display)
    
    /**
     * 刷新内容，根据当前状态决定显示哪种内容
     */
    fun refreshContent()
    
    /**
     * 恢复当前内容
     */
    fun resumeContent()
    
    /**
     * 改变地图类型
     * @param mapType 地图类型
     */
    fun changeMapType(mapType: Int)
    
    /**
     * 改变主题
     * @param themeType 主题类型  
     */
    fun changeTheme(themeType: Int)
    
    /**
     * 销毁控制器，释放所有资源
     */
    fun destroy()
}

/**
 * 内容切换监听器
 */
interface OnContentChangeListener {
    
    /**
     * 内容即将切换
     * @param oldContent 旧内容
     * @param newContent 新内容
     */
    fun onContentChanging(oldContent: IVirtualContentProvider?, newContent: IVirtualContentProvider)
    
    /**
     * 内容已切换完成
     * @param content 当前内容
     */
    fun onContentChanged(content: IVirtualContentProvider)
} 