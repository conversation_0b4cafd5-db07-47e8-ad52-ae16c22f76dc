package com.link.rideramap.virtual

import android.os.Bundle
import android.view.Display
import android.view.View

/**
 * 虚拟内容提供者接口
 * 定义了虚拟地图内容的核心操作
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
interface IVirtualContentProvider {
    
    /**
     * 获取内容视图
     */
    val contentView: View
    
    /**
     * 内容类型
     */
    val contentType: VirtualContentType
    
    /**
     * 创建内容
     * @param bundle 保存的状态数据
     * @param display 显示器信息
     */
    fun onCreate(bundle: Bundle?, display: Display)
    
    /**
     * 恢复内容
     */
    fun onResume()
    
    /**
     * 销毁内容，释放资源
     */
    fun onDestroy()
    
    /**
     * 改变地图类型
     * @param mapType 地图类型
     */
    fun changeMapType(mapType: Int)
    
    /**
     * 改变主题
     * @param themeType 主题类型
     */
    fun changeTheme(themeType: Int)
}

/**
 * 虚拟内容类型枚举
 */
enum class VirtualContentType {
    MAP,        // 普通地图
    NAVIGATION  // 导航地图
} 