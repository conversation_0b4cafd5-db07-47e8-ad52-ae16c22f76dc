package com.link.rideramap.virtual

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.IdRes
import androidx.annotation.LayoutRes

/**
 * 虚拟内容基类
 * 封装了虚拟地图内容的通用逻辑
 * 
 * @property context 上下文
 * @property layoutResId 布局资源ID
 * @property contentType 内容类型
 * 
 * <AUTHOR> Team  
 * @date 2024/1/1
 */
abstract class BaseVirtualContent(
    protected val context: Context,
    @LayoutRes private val layoutResId: Int,
    override val contentType: VirtualContentType
) : IVirtualContentProvider {

    // 根视图
    private val _contentView: View by lazy {
        LayoutInflater.from(context).inflate(layoutResId, null)
    }

    // 阴影视图
    private lateinit var shadowView: View
    
    // 是否已初始化
    private var isInitialized = false
    
    // 是否已销毁
    private var isDestroyed = false

    override val contentView: View
        get() = _contentView

    override fun onCreate(bundle: Bundle?, display: Display) {
        if (isInitialized) {
            Log.w(TAG, "Content already initialized")
            return
        }
        
        try {
            // 初始化阴影视图
            initializeShadowView()
            
            // 子类具体初始化
            onCreateContent(bundle, display)
            
            isInitialized = true
            Log.d(TAG, "Content created successfully: ${contentType.name}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create content: ${contentType.name}", e)
            throw e
        }
    }

    override fun onResume() {
        if (!isInitialized || isDestroyed) {
            Log.w(TAG, "Content not initialized or already destroyed")
            return
        }
        
        try {
            onResumeContent()
            Log.d(TAG, "Content resumed: ${contentType.name}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to resume content: ${contentType.name}", e)
        }
    }

    override fun onDestroy() {
        if (isDestroyed) {
            Log.w(TAG, "Content already destroyed")
            return
        }
        
        try {
            onDestroyContent()
            isDestroyed = true
            Log.d(TAG, "Content destroyed: ${contentType.name}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy content: ${contentType.name}", e)
        }
    }

    override fun changeMapType(mapType: Int) {
        if (!isInitialized || isDestroyed) {
            Log.w(TAG, "Content not ready for map type change")
            return
        }
        
        try {
            onChangeMapType(mapType)
            Log.d(TAG, "Map type changed to: $mapType")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to change map type", e)
        }
    }

    override fun changeTheme(themeType: Int) {
        if (!isInitialized || isDestroyed) {
            Log.w(TAG, "Content not ready for theme change")
            return
        }
        
        try {
            onChangeTheme(themeType)
            Log.d(TAG, "Theme changed to: $themeType")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to change theme", e)
        }
    }

    /**
     * 获取资源
     */
    protected fun getResources(): Resources = context.resources

    /**
     * 根据ID获取视图
     */
    protected fun <T : View> findViewById(@IdRes id: Int): T {
        return _contentView.findViewById(id)
    }

    /**
     * 初始化阴影视图
     */
    private fun initializeShadowView() {
        shadowView = provideShadowView()
        shadowView.setBackgroundColor(DEFAULT_SHADOW_COLOR)
    }

    /**
     * 隐藏阴影视图
     */
    protected fun hideShadowView() {
        shadowView.visibility = View.GONE
    }

    // ================ 抽象方法，子类需要实现 ================

    /**
     * 获取阴影视图
     * 子类需要指定具体的阴影视图
     */
    protected abstract fun provideShadowView(): View

    /**
     * 子类具体的创建逻辑
     */
    protected abstract fun onCreateContent(bundle: Bundle?, display: Display)

    /**
     * 子类具体的恢复逻辑
     */
    protected abstract fun onResumeContent()

    /**
     * 子类具体的轻量级清理逻辑
     * 默认实现调用销毁方法，子类可以重写以实现异步清理
     */
    protected open fun onCleanupContent() {
        onDestroyContent()
    }

    /**
     * 子类具体的销毁逻辑
     */
    protected abstract fun onDestroyContent()

    /**
     * 子类具体的地图类型改变逻辑
     */
    protected abstract fun onChangeMapType(mapType: Int)

    /**
     * 子类具体的主题改变逻辑
     */
    protected abstract fun onChangeTheme(themeType: Int)

    companion object {
        private const val TAG = "BaseVirtualContent"
        
        // 默认阴影颜色
        private val DEFAULT_SHADOW_COLOR = Color.argb(0xFF, 0XEB, 0XEB, 0XEB)
    }
} 