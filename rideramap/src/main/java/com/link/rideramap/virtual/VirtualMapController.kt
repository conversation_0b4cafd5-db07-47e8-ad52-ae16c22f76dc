package com.link.rideramap.virtual

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Display
import android.widget.FrameLayout

/**
 * 虚拟地图控制器实现
 * 预初始化所有内容提供者，通过可见性控制切换，避免频繁创建销毁
 * 
 * @property containerView 容器视图
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
class VirtualMapController(
    private val containerView: FrameLayout
) : IVirtualMapController {

    // 使用统一内容提供者，避免重复创建
    private var unifiedContentProvider: UnifiedVirtualContentProvider? = null
    private var _currentContentProvider: IVirtualContentProvider? = null
    private var contentChangeListener: OnContentChangeListener? = null

    // 保留原有接口兼容性（已废弃，但保留以防其他代码依赖）
    @Deprecated("Use unifiedContentProvider instead")
    private var mapContentProvider: IVirtualContentProvider? = null
    @Deprecated("Use unifiedContentProvider instead")
    private var naviContentProvider: IVirtualContentProvider? = null
    
    private lateinit var context: Context
    private lateinit var savedInstanceState: Bundle
    private lateinit var display: Display
    
    private var isInitialized = false
    private var isDestroyed = false

    override val currentContentProvider: IVirtualContentProvider?
        get() = _currentContentProvider

    override fun initialize(context: Context, bundle: Bundle?, display: Display) {
        val startTime = System.currentTimeMillis()
        if (isInitialized) {
            Log.w(TAG, "Controller already initialized")
            return
        }

        this.context = context
        this.savedInstanceState = bundle ?: Bundle()
        this.display = display

        Log.d(TAG, "Controller initialize start at: $startTime")

        // 使用统一内容提供者，快速完成初始化
        this.isInitialized = true
        Log.d(TAG, "Controller basic initialization completed in ${System.currentTimeMillis() - startTime}ms")

        // 异步初始化统一内容提供者，避免阻塞界面显示
        Handler(Looper.getMainLooper()).post {
            val asyncStartTime = System.currentTimeMillis()
            try {
                Log.d(TAG, "Starting async unified content provider initialization at: $asyncStartTime")
                initializeUnifiedContentProvider()

                // 初始化完成后刷新内容显示
                refreshContent()

                val asyncEndTime = System.currentTimeMillis()
                Log.d(TAG, "Async unified content provider initialization completed in ${asyncEndTime - asyncStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to initialize unified content provider asynchronously", e)
            }
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Controller initialize completed in ${endTime - startTime}ms")
    }

    /**
     * 初始化统一内容提供者
     */
    private fun initializeUnifiedContentProvider() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting unified content provider initialization at: $startTime")

            // 创建统一内容提供者
            unifiedContentProvider = VirtualContentFactory.createUnifiedContentProvider(context).also { provider ->
                containerView.addView(provider.contentView)
                provider.onCreate(savedInstanceState, display)
                _currentContentProvider = provider

                val endTime = System.currentTimeMillis()
                Log.d(TAG, "Unified content provider initialized in ${endTime - startTime}ms")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize unified content provider", e)
            throw e
        }
    }



    override fun refreshContent() {
        if (!isInitialized || isDestroyed) {
            Log.w(TAG, "Controller not ready for content refresh")
            return
        }

        try {
            // 根据导航状态确定要显示的内容类型
            val isNavi = com.link.rideramap.api.RiderMap.instance.isNavi()
            val targetContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP

            // 使用统一内容提供者进行切换
            unifiedContentProvider?.let { provider ->
                if (provider.getCurrentContentType() != targetContentType) {
                    Log.d(TAG, "Switching content type to: ${targetContentType.name}")
                    provider.switchContentType(targetContentType)
                } else {
                    Log.d(TAG, "Content type unchanged: ${targetContentType.name}")
                }
            } ?: run {
                Log.w(TAG, "Unified content provider not initialized")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh content", e)
        }
    }



    override fun resumeContent() {
        unifiedContentProvider?.onResume()
    }

    override fun changeMapType(mapType: Int) {
        // 统一内容提供者会处理所有地图类型更新
        unifiedContentProvider?.changeMapType(mapType)
        Log.d(TAG, "Map type changed to: $mapType")
    }

    override fun changeTheme(themeType: Int) {
        // 统一内容提供者会处理所有主题更新
        unifiedContentProvider?.changeTheme(themeType)
        Log.d(TAG, "Theme changed to: $themeType")
    }

    override fun cleanup() {
        val startTime = System.currentTimeMillis()
        if (isDestroyed) {
            Log.w(TAG, "Controller already destroyed")
            return
        }

        try {
            Log.d(TAG, "Starting controller cleanup at: $startTime")

            // 立即清理关键资源，避免内存泄漏
            val cleanupStartTime = System.currentTimeMillis()
            unifiedContentProvider?.onCleanupContent()
            Log.d(TAG, "Unified content provider cleanup in ${System.currentTimeMillis() - cleanupStartTime}ms")

            // 延迟执行重量级销毁操作
            Handler(Looper.getMainLooper()).post {
                val asyncStartTime = System.currentTimeMillis()
                try {
                    Log.d(TAG, "Starting async destroy at: $asyncStartTime")

                    // 再次检查是否已被销毁，防止重复销毁
                    if (!isDestroyed) {
                        destroyUnifiedContentProvider()
                        isDestroyed = true
                        val asyncEndTime = System.currentTimeMillis()
                        Log.d(TAG, "Controller destroyed asynchronously in ${asyncEndTime - asyncStartTime}ms")
                    } else {
                        Log.w(TAG, "Controller already destroyed, skipping async destroy")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to destroy controller asynchronously", e)
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Controller cleanup completed in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup controller", e)
        }
    }

    override fun destroy() {
        if (isDestroyed) {
            Log.w(TAG, "Controller already destroyed")
            return
        }

        try {
            destroyUnifiedContentProvider()
            isDestroyed = true
            Log.d(TAG, "Controller destroyed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy controller", e)
        }
    }

    /**
     * 设置内容切换监听器
     */
    fun setContentChangeListener(listener: OnContentChangeListener?) {
        this.contentChangeListener = listener
    }

    /**
     * 销毁统一内容提供者
     */
    private fun destroyUnifiedContentProvider() {
        try {
            unifiedContentProvider?.let { provider ->
                containerView.removeView(provider.contentView)
                provider.onDestroy()
                Log.d(TAG, "Unified content provider destroyed")
            }

            // 清空引用
            unifiedContentProvider = null
            _currentContentProvider = null

            Log.d(TAG, "Unified content provider destroyed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy unified content provider", e)
        }
    }

    companion object {
        private const val TAG = "VirtualMapController"
    }
} 