package com.link.rideramap.virtual

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.widget.FrameLayout

/**
 * 虚拟地图控制器实现
 * 预初始化所有内容提供者，通过可见性控制切换，避免频繁创建销毁
 * 
 * @property containerView 容器视图
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
class VirtualMapController(
    private val containerView: FrameLayout
) : IVirtualMapController {

    // 预初始化所有内容提供者
    private var mapContentProvider: IVirtualContentProvider? = null
    private var naviContentProvider: IVirtualContentProvider? = null
    private var _currentContentProvider: IVirtualContentProvider? = null
    private var contentChangeListener: OnContentChangeListener? = null
    
    private lateinit var context: Context
    private lateinit var savedInstanceState: Bundle
    private lateinit var display: Display
    
    private var isInitialized = false
    private var isDestroyed = false

    override val currentContentProvider: IVirtualContentProvider?
        get() = _currentContentProvider

    override fun initialize(context: Context, bundle: Bundle?, display: Display) {
        if (isInitialized) {
            Log.w(TAG, "Controller already initialized")
            return
        }
        
        this.context = context
        this.savedInstanceState = bundle ?: Bundle()
        this.display = display
        
        // 预初始化所有内容提供者
        initializeAllContentProviders()
        
        this.isInitialized = true
        Log.d(TAG, "Controller initialized")
        
        // 初始化后立即刷新内容显示
        refreshContent()
    }

    /**
     * 预初始化所有内容提供者并添加到容器中
     */
    private fun initializeAllContentProviders() {
        try {
            // 创建地图内容提供者
            mapContentProvider = VirtualContentFactory.createContentProvider(
                VirtualContentType.MAP, context
            ).also { provider ->
                containerView.addView(provider.contentView)
                provider.onCreate(savedInstanceState, display)
                provider.contentView.visibility = android.view.View.GONE
                Log.d(TAG, "Map content provider initialized")
            }
            
            // 创建导航内容提供者
            naviContentProvider = VirtualContentFactory.createContentProvider(
                VirtualContentType.NAVIGATION, context
            ).also { provider ->
                containerView.addView(provider.contentView)
                provider.onCreate(savedInstanceState, display)
                provider.contentView.visibility = android.view.View.GONE
                Log.d(TAG, "Navigation content provider initialized")
            }
            
            Log.d(TAG, "All content providers initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize content providers", e)
            throw e
        }
    }

    override fun refreshContent() {
        if (!isInitialized || isDestroyed) {
            Log.w(TAG, "Controller not ready for content refresh")
            return
        }
        
        try {
            // 根据导航状态确定要显示的内容类型
            val isNavi = com.link.rideramap.api.RiderMap.instance.isNavi()
            val targetContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP
            
            // 如果内容类型相同，不需要切换
            if (_currentContentProvider?.contentType == targetContentType) {
                Log.d(TAG, "Content type unchanged, skip refresh")
                return
            }
            
            switchContentByVisibility(targetContentType)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh content", e)
        }
    }

    /**
     * 通过可见性控制切换内容
     */
    private fun switchContentByVisibility(targetContentType: VirtualContentType) {
        val oldContentProvider = _currentContentProvider
        val newContentProvider = when (targetContentType) {
            VirtualContentType.MAP -> mapContentProvider
            VirtualContentType.NAVIGATION -> naviContentProvider
        }
        
        if (newContentProvider == null) {
            Log.w(TAG, "Target content provider is null: $targetContentType")
            return
        }
        
        // 通知监听器内容即将切换
        contentChangeListener?.onContentChanging(oldContentProvider, newContentProvider)
        
        // 隐藏当前内容
        oldContentProvider?.contentView?.visibility = android.view.View.GONE
        
        // 显示新内容
        newContentProvider.contentView.visibility = android.view.View.VISIBLE
        
        // 恢复新内容（因为它可能在后台状态）
        newContentProvider.onResume()
        
        // 更新当前内容提供者
        _currentContentProvider = newContentProvider
        
        // 通知监听器内容已切换
        contentChangeListener?.onContentChanged(newContentProvider)
        
        Log.d(TAG, "Content switched to: ${newContentProvider.contentType.name} (by visibility)")
    }

    override fun resumeContent() {
        _currentContentProvider?.onResume()
    }

    override fun changeMapType(mapType: Int) {
        // 所有内容提供者都需要更新地图类型
        mapContentProvider?.changeMapType(mapType)
        naviContentProvider?.changeMapType(mapType)
        Log.d(TAG, "Map type changed to: $mapType for all providers")
    }

    override fun changeTheme(themeType: Int) {
        // 所有内容提供者都需要更新主题
        mapContentProvider?.changeTheme(themeType)
        naviContentProvider?.changeTheme(themeType)
        Log.d(TAG, "Theme changed to: $themeType for all providers")
    }

    override fun cleanup() {
        val startTime = System.currentTimeMillis()
        if (isDestroyed) {
            Log.w(TAG, "Controller already destroyed")
            return
        }

        try {
            Log.d(TAG, "Starting controller cleanup at: $startTime")

            // 立即清理关键资源，避免内存泄漏
            val cleanupStartTime = System.currentTimeMillis()
            cleanupContentProviders()
            Log.d(TAG, "Content providers cleanup in ${System.currentTimeMillis() - cleanupStartTime}ms")

            // 延迟执行重量级销毁操作
            containerView.post {
                val asyncStartTime = System.currentTimeMillis()
                try {
                    Log.d(TAG, "Starting async destroy at: $asyncStartTime")

                    // 再次检查是否已被销毁，防止重复销毁
                    if (!isDestroyed) {
                        destroyAllContentProviders()
                        isDestroyed = true
                        val asyncEndTime = System.currentTimeMillis()
                        Log.d(TAG, "Controller destroyed asynchronously in ${asyncEndTime - asyncStartTime}ms")
                    } else {
                        Log.w(TAG, "Controller already destroyed, skipping async destroy")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to destroy controller asynchronously", e)
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Controller cleanup completed in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup controller", e)
        }
    }

    override fun destroy() {
        if (isDestroyed) {
            Log.w(TAG, "Controller already destroyed")
            return
        }

        try {
            destroyAllContentProviders()
            isDestroyed = true
            Log.d(TAG, "Controller destroyed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy controller", e)
        }
    }

    /**
     * 设置内容切换监听器
     */
    fun setContentChangeListener(listener: OnContentChangeListener?) {
        this.contentChangeListener = listener
    }

    /**
     * 轻量级清理所有内容提供者的关键资源
     */
    private fun cleanupContentProviders() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting content providers cleanup at: $startTime")

            // 清理地图内容提供者的关键资源
            val mapStartTime = System.currentTimeMillis()
            mapContentProvider?.let { provider ->
                if (provider is BaseVirtualContent) {
                    provider.onCleanupContent()
                }
                Log.d(TAG, "Map content provider cleaned up in ${System.currentTimeMillis() - mapStartTime}ms")
            }

            // 清理导航内容提供者的关键资源
            val naviStartTime = System.currentTimeMillis()
            naviContentProvider?.let { provider ->
                if (provider is BaseVirtualContent) {
                    provider.onCleanupContent()
                }
                Log.d(TAG, "Navigation content provider cleaned up in ${System.currentTimeMillis() - naviStartTime}ms")
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "All content providers cleaned up successfully in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup content providers", e)
        }
    }

    /**
     * 销毁所有内容提供者
     */
    private fun destroyAllContentProviders() {
        try {
            // 销毁地图内容提供者
            mapContentProvider?.let { provider ->
                containerView.removeView(provider.contentView)
                provider.onDestroy()
                Log.d(TAG, "Map content provider destroyed")
            }
            
            // 销毁导航内容提供者
            naviContentProvider?.let { provider ->
                containerView.removeView(provider.contentView)
                provider.onDestroy()
                Log.d(TAG, "Navigation content provider destroyed")
            }
            
            // 清空引用
            mapContentProvider = null
            naviContentProvider = null
            _currentContentProvider = null
            
            Log.d(TAG, "All content providers destroyed successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy content providers", e)
        }
    }

    companion object {
        private const val TAG = "VirtualMapController"
    }
} 