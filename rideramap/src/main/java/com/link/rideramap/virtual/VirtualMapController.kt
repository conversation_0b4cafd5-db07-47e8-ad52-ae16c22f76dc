package com.link.rideramap.virtual

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Display
import android.widget.FrameLayout

/**
 * 虚拟地图控制器实现
 * 预初始化所有内容提供者，通过可见性控制切换，避免频繁创建销毁
 * 
 * @property containerView 容器视图
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
class VirtualMapController(
    private val containerView: FrameLayout
) : IVirtualMapController {

    // 使用统一内容提供者，避免重复创建
    private var unifiedContentProvider: UnifiedVirtualContentProvider? = null
    private var _currentContentProvider: IVirtualContentProvider? = null
    private var contentChangeListener: OnContentChangeListener? = null

    // 保留原有接口兼容性（已废弃，但保留以防其他代码依赖）
    @Deprecated("Use unifiedContentProvider instead")
    private var mapContentProvider: IVirtualContentProvider? = null
    @Deprecated("Use unifiedContentProvider instead")
    private var naviContentProvider: IVirtualContentProvider? = null
    
    private lateinit var context: Context
    private lateinit var savedInstanceState: Bundle
    private lateinit var display: Display
    
    private var isInitialized = false
    private var isDestroyed = false

    override val currentContentProvider: IVirtualContentProvider?
        get() = _currentContentProvider

    override fun initialize(context: Context, bundle: Bundle?, display: Display) {
        val startTime = System.currentTimeMillis()
        if (isInitialized) {
            Log.w(TAG, "Controller already initialized")
            return
        }

        this.context = context
        this.savedInstanceState = bundle ?: Bundle()
        this.display = display

        Log.d(TAG, "Controller initialize start at: $startTime")

        // 使用延迟初始化策略：先快速完成基础初始化，然后异步创建内容提供者
        this.isInitialized = true
        Log.d(TAG, "Controller basic initialization completed in ${System.currentTimeMillis() - startTime}ms")

        // 异步初始化内容提供者，避免阻塞界面显示
        Handler(Looper.getMainLooper()).post {
            val asyncStartTime = System.currentTimeMillis()
            try {
                Log.d(TAG, "Starting async content providers initialization at: $asyncStartTime")
                initializeAllContentProviders()

                // 初始化完成后刷新内容显示
                refreshContent()

                val asyncEndTime = System.currentTimeMillis()
                Log.d(TAG, "Async content providers initialization completed in ${asyncEndTime - asyncStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to initialize content providers asynchronously", e)
            }
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Controller initialize completed in ${endTime - startTime}ms")
    }

    /**
     * 按需初始化内容提供者，优先创建当前需要的类型
     */
    private fun initializeAllContentProviders() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting content providers initialization at: $startTime")

            // 根据当前导航状态决定优先创建哪个内容提供者
            val isNavi = com.link.rideramap.api.RiderMap.instance.isNavi()

            if (isNavi) {
                // 导航模式：优先创建导航内容，延迟创建地图内容
                initializeNaviContentProvider()

                // 延迟创建地图内容提供者
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeMapContentProvider()
                }, 100) // 100ms延迟

            } else {
                // 普通模式：优先创建地图内容，延迟创建导航内容
                initializeMapContentProvider()

                // 延迟创建导航内容提供者
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeNaviContentProvider()
                }, 100) // 100ms延迟
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Priority content provider initialized in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize content providers", e)
            throw e
        }
    }

    /**
     * 初始化地图内容提供者
     */
    private fun initializeMapContentProvider() {
        if (mapContentProvider != null) {
            Log.d(TAG, "Map content provider already initialized")
            return
        }

        val startTime = System.currentTimeMillis()
        try {
            mapContentProvider = VirtualContentFactory.createContentProvider(
                VirtualContentType.MAP, context
            ).also { provider ->
                containerView.addView(provider.contentView)
                provider.onCreate(savedInstanceState, display)
                provider.contentView.visibility = android.view.View.GONE
                val endTime = System.currentTimeMillis()
                Log.d(TAG, "Map content provider initialized in ${endTime - startTime}ms")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize map content provider", e)
        }
    }

    /**
     * 初始化导航内容提供者
     */
    private fun initializeNaviContentProvider() {
        if (naviContentProvider != null) {
            Log.d(TAG, "Navigation content provider already initialized")
            return
        }

        val startTime = System.currentTimeMillis()
        try {
            naviContentProvider = VirtualContentFactory.createContentProvider(
                VirtualContentType.NAVIGATION, context
            ).also { provider ->
                containerView.addView(provider.contentView)
                provider.onCreate(savedInstanceState, display)
                provider.contentView.visibility = android.view.View.GONE
                val endTime = System.currentTimeMillis()
                Log.d(TAG, "Navigation content provider initialized in ${endTime - startTime}ms")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize navigation content provider", e)
        }
    }

    override fun refreshContent() {
        if (!isInitialized || isDestroyed) {
            Log.w(TAG, "Controller not ready for content refresh")
            return
        }

        try {
            // 根据导航状态确定要显示的内容类型
            val isNavi = com.link.rideramap.api.RiderMap.instance.isNavi()
            val targetContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP

            // 如果内容类型相同，不需要切换
            if (_currentContentProvider?.contentType == targetContentType) {
                Log.d(TAG, "Content type unchanged, skip refresh")
                return
            }

            // 检查目标内容提供者是否已初始化
            val targetProvider = if (targetContentType == VirtualContentType.MAP) {
                mapContentProvider
            } else {
                naviContentProvider
            }

            if (targetProvider == null) {
                Log.d(TAG, "Target content provider not ready, initializing...")
                // 如果目标内容提供者未初始化，立即初始化
                if (targetContentType == VirtualContentType.MAP) {
                    initializeMapContentProvider()
                } else {
                    initializeNaviContentProvider()
                }

                // 初始化完成后再次尝试切换
                Handler(Looper.getMainLooper()).post {
                    refreshContent()
                }
                return
            }

            switchContentByVisibility(targetContentType)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh content", e)
        }
    }

    /**
     * 通过可见性控制切换内容
     */
    private fun switchContentByVisibility(targetContentType: VirtualContentType) {
        val oldContentProvider = _currentContentProvider
        val newContentProvider = when (targetContentType) {
            VirtualContentType.MAP -> mapContentProvider
            VirtualContentType.NAVIGATION -> naviContentProvider
        }
        
        if (newContentProvider == null) {
            Log.w(TAG, "Target content provider is null: $targetContentType")
            return
        }
        
        // 通知监听器内容即将切换
        contentChangeListener?.onContentChanging(oldContentProvider, newContentProvider)
        
        // 隐藏当前内容
        oldContentProvider?.contentView?.visibility = android.view.View.GONE
        
        // 显示新内容
        newContentProvider.contentView.visibility = android.view.View.VISIBLE
        
        // 恢复新内容（因为它可能在后台状态）
        newContentProvider.onResume()
        
        // 更新当前内容提供者
        _currentContentProvider = newContentProvider
        
        // 通知监听器内容已切换
        contentChangeListener?.onContentChanged(newContentProvider)
        
        Log.d(TAG, "Content switched to: ${newContentProvider.contentType.name} (by visibility)")
    }

    override fun resumeContent() {
        _currentContentProvider?.onResume()
    }

    override fun changeMapType(mapType: Int) {
        // 所有内容提供者都需要更新地图类型
        mapContentProvider?.changeMapType(mapType)
        naviContentProvider?.changeMapType(mapType)
        Log.d(TAG, "Map type changed to: $mapType for all providers")
    }

    override fun changeTheme(themeType: Int) {
        // 所有内容提供者都需要更新主题
        mapContentProvider?.changeTheme(themeType)
        naviContentProvider?.changeTheme(themeType)
        Log.d(TAG, "Theme changed to: $themeType for all providers")
    }

    override fun cleanup() {
        val startTime = System.currentTimeMillis()
        if (isDestroyed) {
            Log.w(TAG, "Controller already destroyed")
            return
        }

        try {
            Log.d(TAG, "Starting controller cleanup at: $startTime")

            // 立即清理关键资源，避免内存泄漏
            val cleanupStartTime = System.currentTimeMillis()
            cleanupContentProviders()
            Log.d(TAG, "Content providers cleanup in ${System.currentTimeMillis() - cleanupStartTime}ms")

            // 延迟执行重量级销毁操作
            Handler(Looper.getMainLooper()).post {
                val asyncStartTime = System.currentTimeMillis()
                try {
                    Log.d(TAG, "Starting async destroy at: $asyncStartTime")

                    // 再次检查是否已被销毁，防止重复销毁
                    if (!isDestroyed) {
                        destroyAllContentProviders()
                        isDestroyed = true
                        val asyncEndTime = System.currentTimeMillis()
                        Log.d(TAG, "Controller destroyed asynchronously in ${asyncEndTime - asyncStartTime}ms")
                    } else {
                        Log.w(TAG, "Controller already destroyed, skipping async destroy")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to destroy controller asynchronously", e)
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Controller cleanup completed in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup controller", e)
        }
    }

    override fun destroy() {
        if (isDestroyed) {
            Log.w(TAG, "Controller already destroyed")
            return
        }

        try {
            destroyAllContentProviders()
            isDestroyed = true
            Log.d(TAG, "Controller destroyed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy controller", e)
        }
    }

    /**
     * 设置内容切换监听器
     */
    fun setContentChangeListener(listener: OnContentChangeListener?) {
        this.contentChangeListener = listener
    }

    /**
     * 轻量级清理所有内容提供者的关键资源
     */
    private fun cleanupContentProviders() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting content providers cleanup at: $startTime")

            // 清理地图内容提供者的关键资源
            val mapStartTime = System.currentTimeMillis()
            mapContentProvider?.let { provider ->
                if (provider is BaseVirtualContent) {
                    provider.onCleanupContent()
                }
                Log.d(TAG, "Map content provider cleaned up in ${System.currentTimeMillis() - mapStartTime}ms")
            }

            // 清理导航内容提供者的关键资源
            val naviStartTime = System.currentTimeMillis()
            naviContentProvider?.let { provider ->
                if (provider is BaseVirtualContent) {
                    provider.onCleanupContent()
                }
                Log.d(TAG, "Navigation content provider cleaned up in ${System.currentTimeMillis() - naviStartTime}ms")
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "All content providers cleaned up successfully in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup content providers", e)
        }
    }

    /**
     * 销毁所有内容提供者
     */
    private fun destroyAllContentProviders() {
        try {
            // 销毁地图内容提供者
            mapContentProvider?.let { provider ->
                containerView.removeView(provider.contentView)
                provider.onDestroy()
                Log.d(TAG, "Map content provider destroyed")
            }
            
            // 销毁导航内容提供者
            naviContentProvider?.let { provider ->
                containerView.removeView(provider.contentView)
                provider.onDestroy()
                Log.d(TAG, "Navigation content provider destroyed")
            }
            
            // 清空引用
            mapContentProvider = null
            naviContentProvider = null
            _currentContentProvider = null
            
            Log.d(TAG, "All content providers destroyed successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy content providers", e)
        }
    }

    companion object {
        private const val TAG = "VirtualMapController"
    }
} 