package com.link.rideramap.virtual

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.View
import android.widget.FrameLayout
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.map.NaviCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 统一虚拟内容提供者
 * 将地图和导航内容合并到一个XML中，通过可见性控制切换
 * 避免重复创建和销毁，提升性能
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
class UnifiedVirtualContentProvider(context: Context) : BaseVirtualContent(
    context,
    R.layout.virtual_unified_view,
    VirtualContentType.MAP // 默认类型，会根据状态动态切换
) {
    // 地图相关组件
    private lateinit var virtualMap: VirtualMap
    private lateinit var aMap: AMap
    private var marker: Marker? = null
    private var locationJob: Job? = null
    
    // 导航相关组件
    private lateinit var virtualNaviMap: VirtualNaviMap
    private var virtualNaviView: VirtualNaviView? = null
    
    // 容器视图
    private lateinit var mapContainer: FrameLayout
    private lateinit var naviContainer: FrameLayout
    
    // 状态管理
    private var currentContentType = VirtualContentType.MAP
    private var isMapInitialized = false
    private var isNaviInitialized = false
    private var isMapDestroyed = false
    private var isNaviMapDestroyed = false
    
    // 导航数据回调
    private val naviDataCallback = object : NaviCallback() {
        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            virtualNaviView?.onNaviDataChanged(navigationInfo)
        }

        override fun onGpsSignalWeak(isWeek: Boolean) {
            super.onGpsSignalWeak(isWeek)
            virtualNaviView?.onGpsSignalWeak(isWeek)
        }
    }

    override fun provideShadowView(): View {
        return findViewById(R.id.v_unified_shadow)
    }

    override fun onCreateContent(bundle: Bundle?, display: Display) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "onCreateContent start at: $startTime")
        
        try {
            // 初始化容器视图
            mapContainer = findViewById(R.id.map_container)
            naviContainer = findViewById(R.id.navi_container)
            
            // 根据当前状态决定优先初始化哪个内容
            val isNavi = RiderMap.instance.isNavi()
            currentContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP
            
            if (isNavi) {
                // 导航模式：优先初始化导航内容
                initializeNaviContent(bundle, display)
                // 延迟初始化地图内容
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeMapContent(bundle, display)
                }, 50)
            } else {
                // 普通模式：优先初始化地图内容
                initializeMapContent(bundle, display)
                // 延迟初始化导航内容
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeNaviContent(bundle, display)
                }, 50)
            }
            
            // 设置初始可见性
            updateContentVisibility()
            
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "onCreateContent completed in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create unified content", e)
            throw e
        }
    }
    
    /**
     * 初始化地图内容
     */
    private fun initializeMapContent(bundle: Bundle?, display: Display) {
        if (isMapInitialized) {
            Log.d(TAG, "Map content already initialized")
            return
        }
        
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Initializing map content at: $startTime")
            
            // 初始化虚拟地图
            virtualMap = findViewById(R.id.virtual_map)
            aMap = virtualMap.map.apply {
                uiSettings.isZoomControlsEnabled = false
                uiSettings.isIndoorSwitchEnabled = false
                uiSettings.isZoomGesturesEnabled = false
                showBuildings(false)
                setRenderFps(15)
            }
            
            // 设置地图类型
            aMap.mapType = RiderMap.instance.getDefaultMode()
            
            // 设置地图可见监听器
            virtualMap.setActionListener(object : IActionListener {
                override fun onVisible() {
                    hideShadowView()
                }
            })
            
            // 创建地图
            virtualMap.create(bundle)
            
            // 设置当前位置
            setupLocation()
            
            isMapInitialized = true
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Map content initialized in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize map content", e)
        }
    }
    
    /**
     * 初始化导航内容
     */
    private fun initializeNaviContent(bundle: Bundle?, display: Display) {
        if (isNaviInitialized) {
            Log.d(TAG, "Navigation content already initialized")
            return
        }
        
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Initializing navigation content at: $startTime")
            
            // 初始化导航地图
            virtualNaviMap = findViewById(R.id.virtual_navi_map)
            virtualNaviMap.create(bundle)
            
            // 设置地图可见监听器
            virtualNaviMap.setActionListener(object : IActionListener {
                override fun onVisible() {
                    hideShadowView()
                    setupNaviViewOptions()
                }
            })
            
            // 添加导航UI视图
            addVirtualNaviView(display)
            
            // 注册导航数据回调
            RiderMap.instance.addNaviDataCallback(naviDataCallback)
            
            isNaviInitialized = true
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Navigation content initialized in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize navigation content", e)
        }
    }
    
    /**
     * 根据屏幕方向添加对应的导航视图
     */
    private fun addVirtualNaviView(display: Display) {
        val displayMetrics = DisplayMetrics()
        display.getMetrics(displayMetrics)
        
        virtualNaviView = if (displayMetrics.widthPixels < displayMetrics.heightPixels) {
            VirtualNaviPortView(context) // 竖屏
        } else {
            VirtualNaviLandView(context) // 横屏
        }
        
        findViewById<FrameLayout>(R.id.navi_ui_container).addView(virtualNaviView)
    }
    
    /**
     * 设置导航视图选项
     */
    private fun setupNaviViewOptions() {
        val mapType = RiderMap.instance.getDefaultModeNavi()
        val viewOptions = virtualNaviMap.viewOptions.apply {
            isSettingMenuEnabled = false
            isLayoutVisible = false
            isTrafficBarEnabled = false
            isAutoChangeZoom = false
            setModeCrossDisplayShow(false)
            isRealCrossDisplayShow = false
            isAfterRouteAutoGray = true
            isAutoLockCar = true
            isDrawBackUpOverlay = false
            lockMapDelayed = 7000L
            tilt = 35
            carBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.amap_navmylocation)
            isAutoNaviViewNightMode = false
            isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
        }
        virtualNaviMap.viewOptions = viewOptions
    }
    
    /**
     * 设置位置信息
     */
    private fun setupLocation() {
        // 先尝试使用缓存的位置
        val cachedLocation = RiderMap.instance.getLocation()
        cachedLocation?.let {
            setPosition(it.latitude, it.longitude)
        }
        
        // 异步获取最新位置
        locationJob = MainScope().launch(Dispatchers.IO) {
            try {
                val locationInfo = RiderMap.instance.getLbsLocation()
                setPosition(locationInfo.latitude, locationInfo.longitude)
                RiderMap.instance.setLocation(locationInfo)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get location", e)
            }
        }
    }
    
    /**
     * 设置地图位置
     */
    private fun setPosition(latitude: Double, longitude: Double) {
        if (!isMapInitialized) return
        
        val latLng = LatLng(latitude, longitude)
        aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 17f))
        updateMarker(latLng)
    }
    
    /**
     * 更新位置标记
     */
    private fun updateMarker(latLng: LatLng) {
        marker?.remove()
        
        val markerOptions = MarkerOptions().apply {
            anchor(0.5f, 0.5f)
            isFlat = true
            visible(true)
            zIndex(2.2f)
            position(latLng)
            
            // 设置标记图标
            val locationBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.nearme)
            icon(BitmapDescriptorFactory.fromBitmap(locationBitmap))
        }
        
        marker = aMap.addMarker(markerOptions)
    }

    override fun onResumeContent() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                if (isMapInitialized) {
                    virtualMap.onResume()
                }
            }
            VirtualContentType.NAVIGATION -> {
                if (isNaviInitialized) {
                    virtualNaviMap.onResume()
                }
            }
        }
    }

    /**
     * 切换内容类型
     */
    fun switchContentType(targetType: VirtualContentType) {
        if (currentContentType == targetType) {
            Log.d(TAG, "Content type unchanged: ${targetType.name}")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching content type from ${currentContentType.name} to ${targetType.name} at: $startTime")

        currentContentType = targetType
        updateContentVisibility()

        // 恢复目标内容
        when (targetType) {
            VirtualContentType.MAP -> {
                if (isMapInitialized) {
                    virtualMap.onResume()
                }
            }
            VirtualContentType.NAVIGATION -> {
                if (isNaviInitialized) {
                    virtualNaviMap.onResume()
                }
            }
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Content type switched in ${endTime - startTime}ms")
    }

    /**
     * 更新内容可见性
     */
    private fun updateContentVisibility() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                mapContainer.visibility = View.VISIBLE
                naviContainer.visibility = View.GONE
            }
            VirtualContentType.NAVIGATION -> {
                mapContainer.visibility = View.GONE
                naviContainer.visibility = View.VISIBLE
            }
        }
    }

    override fun onCleanupContent() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "onCleanupContent start at: $startTime")

        try {
            // 立即清理关键资源
            locationJob?.cancel()
            marker?.remove()
            virtualNaviView = null

            // 异步销毁地图资源
            if (!isMapDestroyed && isMapInitialized) {
                isMapDestroyed = true
                Handler(Looper.getMainLooper()).post {
                    val mapDestroyStartTime = System.currentTimeMillis()
                    try {
                        virtualMap.destroy()
                        Log.d(TAG, "VirtualMap destroyed in ${System.currentTimeMillis() - mapDestroyStartTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual map", e)
                    }
                }
            }

            // 异步销毁导航地图资源
            if (!isNaviMapDestroyed && isNaviInitialized) {
                isNaviMapDestroyed = true
                Handler(Looper.getMainLooper()).post {
                    val naviDestroyStartTime = System.currentTimeMillis()
                    try {
                        // 移除导航数据回调
                        RiderMap.instance.removeNaviDataCallback(naviDataCallback)
                        // 销毁导航地图
                        virtualNaviMap.destroy()
                        Log.d(TAG, "VirtualNaviMap destroyed in ${System.currentTimeMillis() - naviDestroyStartTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual navi map", e)
                    }
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "onCleanupContent completed in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup unified content", e)
        }
    }

    override fun onDestroyContent() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "onDestroyContent start at: $startTime")

        try {
            // 同步销毁所有资源
            locationJob?.cancel()
            marker?.remove()

            if (!isMapDestroyed && isMapInitialized) {
                isMapDestroyed = true
                virtualMap.destroy()
            }

            if (!isNaviMapDestroyed && isNaviInitialized) {
                isNaviMapDestroyed = true
                RiderMap.instance.removeNaviDataCallback(naviDataCallback)
                virtualNaviMap.destroy()
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "onDestroyContent completed in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy unified content", e)
        }
    }

    override fun onChangeMapType(mapType: Int) {
        // 更新地图类型
        if (isMapInitialized) {
            aMap.mapType = mapType
        }

        // 更新导航地图类型
        if (isNaviInitialized) {
            val options = virtualNaviMap.viewOptions.apply {
                isAutoNaviViewNightMode = false
                isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
            }
            virtualNaviMap.viewOptions = options
        }
    }

    override fun onChangeTheme(themeType: Int) {
        // TODO: 实现主题切换逻辑
    }

    /**
     * 获取当前内容类型
     */
    fun getCurrentContentType(): VirtualContentType {
        return currentContentType
    }

    companion object {
        private const val TAG = "UnifiedVirtualContent"
    }
}
