package com.link.rideramap.api

import android.Manifest
import android.app.Application
import android.content.Context
import android.util.Log
import android.view.Display
import android.view.WindowManager
import com.amap.api.maps.AMap
import com.amap.api.maps.MapsInitializer
import com.amap.api.navi.enums.TravelStrategy
import com.amap.api.navi.model.AMapLaneInfo
import com.amap.api.navi.model.AMapNaviCross
import com.amap.api.navi.model.AMapNaviRouteNotifyData
import com.amap.api.navi.model.NaviPoi
import com.amap.api.services.core.ServiceSettings
import com.link.rideramap.R
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.map.MapManager
import com.link.rideramap.map.NaviCallback
import com.link.rideramap.map.location.domain.entity.LocationInfo
import com.link.rideramap.map.weather.domain.entity.AMapWeatherData
import com.link.rideramap.mirror.MirrorBasePresentation
import com.link.rideramap.mirror.VirtualMapPresentation
import java.lang.ref.WeakReference

/**
 * Created on 2023/1/11.
 * <AUTHOR>
 */
class RiderMap {
    private val mNaviDataManager = MapManager()
    private var mPresentation: MirrorBasePresentation? = null
    private val mCallbacks: MutableList<WeakReference<MapCallback>> = mutableListOf()
    private var mApplication: Application? = null
    private var defaultMode = NAVI_DAYTIME
    private var myLocation:  LocationInfo? = null
    private var onNaviThemeChange = false
    /**
     * 传入 [application],并初始化 RiderMap
     */
    internal fun init(application: Application?) {
        MapsInitializer.updatePrivacyShow(application, true, true)
        MapsInitializer.updatePrivacyAgree(application, true)
        ServiceSettings.updatePrivacyShow(application, true, true)
        ServiceSettings.updatePrivacyAgree(application, true)
        ServiceSettings.getInstance().connectionTimeOut = TIMEOUT
        ServiceSettings.getInstance().soTimeOut = TIMEOUT
        mApplication = application
        mNaviDataManager.addNaviDataCallback(mNaviDataCallback)
    }
    fun getDefaultMode(): Int {
        return defaultMode
    }

    fun getDefaultModeNavi(): Int {
        return defaultMode
    }
    /**
     * 销毁 [RiderMap]
     */
    fun destroy() {
        mNaviDataManager.removePathRoutesListener()
        mNaviDataManager.removeNaviDataCallback(mNaviDataCallback)
        mApplication = null
    }

    /**
     * 获取 [Application]
     */
    fun getApplication(): Application {
        return mApplication!!
    }

    /**
     * 添加回调 [callback]
     * @see MapCallback
     */
    @Synchronized
    fun addCallback(callback: MapCallback) {
        mCallbacks.add(WeakReference(callback))
    }

    /**
     * 移除回调 [callback]
     * @see MapCallback
     */
    @Synchronized
    fun removeCallback(callback: MapCallback) {
        mCallbacks.removeIf { it.get() == callback }
    }


    /**
     * 通过 [display] 初始化投屏导航
     */
    @Synchronized
    fun initNaviScreenProjection(display: Display?, isSupportCircularScreen: Boolean = false) {
        Log.d(TAG, "initNaviScreenProjection")
        try {
            if (!(mPresentation == null || mPresentation?.display === display)) {
                Log.i(
                    TAG,
                    "Dismissing presentation because the current route no longer has a presentation display."
                )
                if (mPresentation?.isShowing == true) {
                    mPresentation?.dismiss()
                }
                mPresentation = null
            }
        } catch (th: Throwable) {
            Log.e(TAG, "initPresentation failed", th)
        }

        if (mPresentation == null && display != null) {
            val createMirrorPresentation: MirrorBasePresentation =
                createMirrorPresentation(
                    getApplication(),
                    display,
                    R.style.PresentationDialog,
                    isSupportCircularScreen
                )
            mPresentation = createMirrorPresentation
            try {
                createMirrorPresentation.show()
            } catch (e: WindowManager.InvalidDisplayException) {
                Log.e(TAG, "Couldn't show presentation!  Display was removed in the meantime.", e)
                mPresentation = null
            }
        }
    }

    private fun createMirrorPresentation(
        context: Context, display: Display, theme: Int, isSupportCircularScreen: Boolean
    ): MirrorBasePresentation {
        return VirtualMapPresentation(context, display, theme, isSupportCircularScreen)
    }


    /**
     * 销毁投屏导航
     */
    @Synchronized
    fun releaseNaviScreenProjection() {
        try {
            Log.i(TAG, "releasePresentation $mPresentation")
            mPresentation?.dismiss()
            mPresentation = null
        } catch (th: Throwable) {
            Log.e(TAG, "releasePresentation", th)
        }
    }

    /**
     * 通过地理编码搜索
     * @param context Context
     * @param latitude 纬度
     * @param longitude 经度
     * @return 返回位置信息
     * @see LocationInfo
     */
    suspend fun geocodeSearch(
        context: Context, latitude: Double, longitude: Double
    ): LocationInfo? {
        return mNaviDataManager.geocodeSearch(context, latitude, longitude)
    }

    /**
     * 通过关键字搜索 POI
     * @param keyWord 关键字
     * @return 返回搜索结果
     * @see SearchResult
     */
    suspend fun search(keyWord: String): SearchResult {
        return mNaviDataManager.search(getApplication(), keyWord)
    }

    /**
     * 通过 POI 搜索
     * @param searchAddress 搜索地址
     * @return 返回搜索结果
     * @see PoiAddress
     * @see SearchResult
     */
    suspend fun searchPOI(searchAddress: PoiAddress): SearchResult {
        return mNaviDataManager.searchPOI(getApplication(), searchAddress)
    }
    /**
     *更改地图模式
     */
    fun changeVirtualNaviMap(type: Int, ontheme: Boolean = false): Int{
        if (onNaviThemeChange && ontheme){
            onNaviThemeChange = false
            return defaultMode
        }
        when(type){
            NAVI_DAYTIME -> defaultMode = AMap.MAP_TYPE_NORMAL
            NAVI_NIGHT -> defaultMode = AMap.MAP_TYPE_NIGHT
//            NAVI_AUTO -> defaultMode = NAVIMODE_AUTO
        }
        mPresentation?.changeMap(defaultMode)
        return defaultMode
    }
    /**
     * 停止导航
     */
    fun stopNavi() {
        mNaviDataManager.stopNavi()
    }


    /**
     * 选择导航路径
     * @param routeId 路径 id
     */
    fun selectRoute(routeId: Int) {
        mNaviDataManager.selectRoute(routeId)
    }

    /**
     * 传入 [from] 和 [to],计算骑行导航路径,并通过 [planRoutesListener] 回调结果
     * @param from 起点
     * @param to 终点
     * @param planRoutesListener 计算路径结果回调
     */
    fun calculateRideRoute(
        from: NaviPoi,
        to: NaviPoi,
        travelStrategy: TravelStrategy,
        planRoutesListener: PlanRoutesListener
    ) {
        mNaviDataManager.calculateRideRoute(
            getApplication(), from, to, travelStrategy, planRoutesListener
        )
    }


    /**
     * 传入 [from] 和 [to],计算骑行导航路径,并通过 [planRoutesListener] 回调结果
     * @param from 起点
     * @param to 终点
     * @param planRoutesListener 计算路径结果回调
     */
    fun calculateDriveRoute(
        from: NaviPoi,
        to: NaviPoi,
        pathPlanningStrategy: Int,
        planRoutesListener: PlanRoutesListener
    ) {
        mNaviDataManager.calculateDriveRoute(
            getApplication(), from, to, pathPlanningStrategy, planRoutesListener
        )
    }
    /**
     *之前保留的locationInfo
     */
    fun getLocation(): LocationInfo? {
        return myLocation
    }
    /**
     *更新保留的locationInfo
     */
    fun setLocation(locationInfo: LocationInfo) {
        myLocation = locationInfo
    }

    /**
     * 添加导航数据回调
     * @param naviCallback 导航数据回调
     * @see NaviCallback
     */
    internal fun addNaviDataCallback(naviCallback: NaviCallback) {
        mNaviDataManager.addNaviDataCallback(naviCallback)
    }

    internal fun removeNaviDataCallback(naviCallback: NaviCallback) {
        mNaviDataManager.removeNaviDataCallback(naviCallback)
    }
    /**
     * 获取定位位置
     * 需要权限 [Manifest.permission.ACCESS_FINE_LOCATION]
     * 需要权限 [Manifest.permission.ACCESS_COARSE_LOCATION]
     * @return 返回位置信息
     * @see LocationInfo
     */
    suspend fun getLbsLocation(): LocationInfo {
        return mNaviDataManager.getLbsLocation()
    }

    suspend fun startLbsLocation(): LocationInfo {
        return mNaviDataManager.startLbsLocation()
    }

    internal fun stopLbsLocation() {
        mNaviDataManager.stopLbsLocation()
    }

    fun startNavi() {
        mNaviDataManager.startNavi()
        mPresentation?.changeMap(defaultMode)
    }

    fun setNaviType(type: Boolean) {
        mNaviDataManager.setNaviType(type)
    }

    fun isNavi(): Boolean {
        return mNaviDataManager.isNavi()
    }


    private val mNaviDataCallback = object : NaviCallback() {

        override fun onGpsSignalWeak(isWeek: Boolean) {
            super.onGpsSignalWeak(isWeek)
            mCallbacks.forEach {
                it.get()?.onGpsSignalWeak(isWeek)
            }
        }

        override fun onInitNaviSuccess() {
            super.onInitNaviSuccess()
            mCallbacks.forEach {
                it.get()?.onInitNaviSuccess()
            }
        }

        override fun onGetNavigationText(type: Int, s: String) {
            super.onGetNavigationText(type, s)
            mCallbacks.forEach {
                it.get()?.onGetNavigationText(type, s)
            }
        }

        override fun showLaneInfo(aMapLaneInfo: AMapLaneInfo) {
            super.showLaneInfo(aMapLaneInfo)
            mCallbacks.forEach {
                it.get()?.showLaneInfo(
                    aMapLaneInfo
                )
            }
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            mCallbacks.forEach {
                it.get()?.onArriveDestination()
            }
        }

        override fun onNaviRouteNotify(aMapNaviRouteNotifyData: AMapNaviRouteNotifyData) {
            super.onNaviRouteNotify(aMapNaviRouteNotifyData)
            mCallbacks.forEach {
                it.get()?.onNaviRouteNotify(aMapNaviRouteNotifyData)
            }
        }

        override fun showCross(aMapNaviCross: AMapNaviCross) {
            super.showCross(aMapNaviCross)
            mCallbacks.forEach {
                it.get()?.showCross(aMapNaviCross)
            }
        }


        override fun onStartNavi() {
            super.onStartNavi()
            mCallbacks.forEach {
                it.get()?.onStartNavi()
            }
        }

        override fun onStopNavi() {
            super.onStopNavi()
            mCallbacks.forEach {
                it.get()?.onStopNavi()
            }
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            mCallbacks.forEach {
                it.get()?.onEndEmulatorNavi()
            }
        }

        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            mCallbacks.forEach {
                it.get()?.onNaviDataChanged(navigationInfo)
            }
        }
    }

    fun onMapchange(type: Int){//点击按钮切换地图昼夜模式
        val realtype = changeVirtualNaviMap(type)
        onNaviThemeChange = true
        mCallbacks.forEach{
            it.get()?.changeMap(realtype)
        }
    }

    suspend fun getWeatherInfo(): AMapWeatherData {
        val weather = mNaviDataManager.getWeather()
        return weather.getOrNull() ?: AMapWeatherData()
    }

    companion object {
        /** 获取 RiderMap 的单例 */
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            RiderMap()
        }
        private const val TIMEOUT = 5000
        private const val TAG = "RiderMap"
        /**地图昼夜自动模式: 4*/
//        private const val NAVIMODE_AUTO = 4

        /**协议昼夜白天模式: 0*/
        private const val NAVI_DAYTIME = 0

        /**协议昼夜黑夜模式: 1*/
        private const val NAVI_NIGHT = 1

        /**协议昼夜自动模式: 2*/
//        private const val NAVI_AUTO = 2
    }
}