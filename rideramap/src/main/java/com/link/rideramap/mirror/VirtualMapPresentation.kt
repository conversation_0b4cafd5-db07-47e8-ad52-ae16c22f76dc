package com.link.rideramap.mirror

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import com.link.rideramap.R


internal class VirtualMapPresentation @JvmOverloads constructor(
    context: Context,
    display: Display,
    theme: Int = 0,
    val isSupportCircularScreen: Boolean
) : MirrorBasePresentation(context, display, theme) {
    private var contentManager: VirtualContentManager? = null
    private var rootView: View? = null

    public override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        Log.d(TAG, "VirtualMapPresentation onCreate start at: $startTime")

        // 设置布局（包含统一的地图和导航内容）
        setContentView(R.layout.root_virtual_main)
        val layoutTime = System.currentTimeMillis()
        Log.d(TAG, "Layout inflated in ${layoutTime - startTime}ms")

        // 获取根视图
        rootView = findViewById(R.id.virtual_root)
        val viewTime = System.currentTimeMillis()
        Log.d(TAG, "Root view obtained in ${viewTime - layoutTime}ms")

        // 创建并初始化内容管理器
        val initStartTime = System.currentTimeMillis()
        contentManager = VirtualContentManager(context, rootView!!)
        contentManager?.initialize(savedInstanceState, display)
        val initEndTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualContentManager initialized in ${initEndTime - initStartTime}ms")

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualMapPresentation onCreate completed in ${endTime - startTime}ms")
    }

    override fun onStart() {
        super.onStart()
        contentManager?.onResume()
    }

    override fun onStop() {
        super.onStop()
        // 暂停时不需要特殊处理，保持当前状态
    }


    override fun show() {
        super.show()
        Log.d(TAG, "show screen")
    }

    override fun getRootView(): View? {
        return rootView
    }

    override fun changeMap(type: Int) {
        contentManager?.changeMapType(type)
        Log.d(TAG, "changeMap: $type")
    }

    override fun dismiss() {
        val startTime = System.currentTimeMillis()
        super.dismiss()
        Log.d(TAG, "dismiss in - start time: $startTime")

        // 异步执行清理操作，避免阻塞dismiss
        contentManager?.cleanup()
        contentManager = null
        rootView = null

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "dismiss screen completed in ${endTime - startTime}ms")
    }

    companion object {
        private const val TAG = "VirtualMapPresentation"
    }

    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window?.addFlags(WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window?.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
    }
}