package com.link.rideramap.mirror

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import com.link.rideramap.R
import com.link.rideramap.common.base.IVirtualHomeView
import com.link.rideramap.virtual.VirtualHomeView


/**
 * <AUTHOR>
 * @date 2022/6/29
 */

internal class VirtualMapPresentation @JvmOverloads constructor(
    context: Context,
    display: Display,
    theme: Int = 0,
    val isSupportCircularScreen: Boolean
) : MirrorBasePresentation(context, display, theme) {
    private var mVirtualHomeView: IVirtualHomeView? = null

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.root_virtual_main)
        mVirtualHomeView = VirtualHomeView(context)
        findViewById<FrameLayout>(R.id.virtual_root).addView(mVirtualHomeView)
        mVirtualHomeView?.onCreate(savedInstanceState, display)
    }

    override fun onStart() {
        super.onStart()
        mVirtualHomeView?.onStart()
    }

    override fun onStop() {
        super.onStop()
        mVirtualHomeView?.onStop()
    }


    override fun show() {
        super.show()
        Log.d(TAG, "show screen")
    }

    override fun getRootView(): View? {
        return mVirtualHomeView
    }

    override fun changeMap(type: Int) {
        mVirtualHomeView?.changeMap(type)
        Log.e(TAG, "changeMap: ", )
    }

    override fun dismiss() {
        val startTime = System.currentTimeMillis()
        super.dismiss()
        Log.d(TAG, "dismiss in - start time: $startTime")

        // 异步执行销毁操作，避免阻塞dismiss
        mVirtualHomeView?.post {
            val asyncStartTime = System.currentTimeMillis()
            try {
                Log.d(TAG, "Starting async cleanup at: $asyncStartTime")
                mVirtualHomeView?.onDestroy()
                mVirtualHomeView = null
                val asyncEndTime = System.currentTimeMillis()
                Log.d(TAG, "dismiss screen - async cleanup completed in ${asyncEndTime - asyncStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to cleanup VirtualHomeView", e)
            }
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "dismiss screen completed in ${endTime - startTime}ms")
    }

    companion object {
        private const val TAG = "VirtualMapPresentation"
    }

    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window?.addFlags(WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window?.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
    }
}