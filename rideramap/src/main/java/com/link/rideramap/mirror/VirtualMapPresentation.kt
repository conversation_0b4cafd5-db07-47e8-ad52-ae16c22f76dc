package com.link.rideramap.mirror

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import com.link.rideramap.R


/**
 * <AUTHOR>
 * @date 2022/6/29
 */

internal class VirtualMapPresentation @JvmOverloads constructor(
    context: Context,
    display: Display,
    theme: Int = 0,
    val isSupportCircularScreen: Boolean
) : MirrorBasePresentation(context, display, theme) {
    private var mVirtualHomeView: IVirtualHomeView? = null

    public override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        Log.d(TAG, "VirtualMapPresentation onCreate start at: $startTime")

        // 设置布局（现在包含了统一的地图和导航内容）
        setContentView(R.layout.root_virtual_main)
        val layoutTime = System.currentTimeMillis()
        Log.d(TAG, "Layout inflated in ${layoutTime - startTime}ms")

        // 创建并初始化VirtualHomeView
        mVirtualHomeView = VirtualHomeView(context)
        findViewById<FrameLayout>(R.id.virtual_root).addView(mVirtualHomeView)
        val viewTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualHomeView created and added in ${viewTime - layoutTime}ms")

        // 初始化VirtualHomeView（内部已经优化为异步）
        val initStartTime = System.currentTimeMillis()
        mVirtualHomeView?.onCreate(savedInstanceState, display)
        val initEndTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualHomeView initialized in ${initEndTime - initStartTime}ms")

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualMapPresentation onCreate completed in ${endTime - startTime}ms")
    }

    override fun onStart() {
        super.onStart()
        mVirtualHomeView?.onStart()
    }

    override fun onStop() {
        super.onStop()
        mVirtualHomeView?.onStop()
    }


    override fun show() {
        super.show()
        Log.d(TAG, "show screen")
    }

    override fun getRootView(): View? {
        return mVirtualHomeView
    }

    override fun changeMap(type: Int) {
        mVirtualHomeView?.changeMap(type)
        Log.e(TAG, "changeMap: ", )
    }

    override fun dismiss() {
        super.dismiss()
        Log.d(TAG, "dismiss in")
        mVirtualHomeView?.onDestroy()
        mVirtualHomeView = null
        Log.d(TAG, "dismiss  screen")
    }

    companion object {
        private const val TAG = "VirtualMapPresentation"
    }

    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window?.addFlags(WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window?.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
    }
}