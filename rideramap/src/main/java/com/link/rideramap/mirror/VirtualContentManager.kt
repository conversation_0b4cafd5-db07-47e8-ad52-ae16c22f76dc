package com.link.rideramap.mirror

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.View
import android.widget.FrameLayout
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.map.NaviCallback
import com.link.rideramap.virtual.IActionListener
import com.link.rideramap.virtual.VirtualContentType
import com.link.rideramap.virtual.VirtualMap
import com.link.rideramap.virtual.VirtualNaviLandView
import com.link.rideramap.virtual.VirtualNaviMap
import com.link.rideramap.virtual.VirtualNaviPortView
import com.link.rideramap.virtual.VirtualNaviView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 虚拟内容管理器
 * 直接管理地图和导航内容，避免复杂的中间层
 * 
 * <AUTHOR> Team
 * @date 2024/1/1
 */
class VirtualContentManager(
    private val context: Context,
    private val rootView: View
) {
    // 容器视图
    private var mapContainer: FrameLayout? = null
    private var naviContainer: FrameLayout? = null
    private var shadowView: View? = null
    
    // 地图组件
    private var virtualMap: VirtualMap? = null
    private var aMap: AMap? = null
    private var marker: Marker? = null
    private var locationJob: Job? = null
    
    // 导航组件
    private var virtualNaviMap: VirtualNaviMap? = null
    private var virtualNaviView: VirtualNaviView? = null
    
    // 状态管理
    private var currentContentType = VirtualContentType.MAP
    private var isMapInitialized = false
    private var isNaviInitialized = false
    private var isMapDestroyed = false
    private var isNaviMapDestroyed = false
    
    // 导航数据回调
    private val naviDataCallback = object : NaviCallback() {
        override fun onStartNavi() {
            super.onStartNavi()
            Log.d(TAG, "onStartNavi")
            switchToNavigation()
        }

        override fun onStopNavi() {
            super.onStopNavi()
            Log.d(TAG, "onStopNavi")
            switchToMap()
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            Log.d(TAG, "onEndEmulatorNavi")
            switchToMap()
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            Log.d(TAG, "onArriveDestination")
            switchToMap()
        }
        
        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            virtualNaviView?.onNaviDataChanged(navigationInfo)
        }

        override fun onGpsSignalWeak(isWeek: Boolean) {
            super.onGpsSignalWeak(isWeek)
            virtualNaviView?.onGpsSignalWeak(isWeek)
        }
    }
    
    /**
     * 初始化内容管理器
     */
    fun initialize(bundle: Bundle?, display: Display) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualContentManager initialize start at: $startTime")
        
        try {
            // 初始化容器视图
            initializeContainers()
            
            // 初始化阴影视图
            initializeShadowView()
            
            // 根据当前状态决定优先初始化哪个内容
            val isNavi = RiderMap.instance.isNavi()
            currentContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP
            
            if (isNavi) {
                // 导航模式：优先初始化导航内容
                initializeNaviContent(bundle, display)
                // 延迟初始化地图内容
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeMapContent(bundle, display)
                }, 50)
            } else {
                // 普通模式：优先初始化地图内容
                initializeMapContent(bundle, display)
                // 延迟初始化导航内容
                Handler(Looper.getMainLooper()).postDelayed({
                    initializeNaviContent(bundle, display)
                }, 50)
            }
            
            // 设置初始可见性
            updateContentVisibility()
            
            // 注册导航状态监听
            RiderMap.instance.addNaviDataCallback(naviDataCallback)
            
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualContentManager initialized in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize VirtualContentManager", e)
            throw e
        }
    }
    
    /**
     * 初始化容器视图
     */
    private fun initializeContainers() {
        mapContainer = rootView.findViewById(R.id.map_container)
        naviContainer = rootView.findViewById(R.id.navi_container)
        shadowView = rootView.findViewById(R.id.v_map_shadow)
    }
    
    /**
     * 初始化阴影视图
     */
    private fun initializeShadowView() {
        shadowView?.setBackgroundColor(android.graphics.Color.argb(0xFF, 0XEB, 0XEB, 0XEB))
    }
    
    /**
     * 初始化地图内容
     */
    private fun initializeMapContent(bundle: Bundle?, display: Display) {
        if (isMapInitialized) {
            Log.d(TAG, "Map content already initialized")
            return
        }
        
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Initializing map content at: $startTime")
            
            // 初始化虚拟地图
            virtualMap = rootView.findViewById(R.id.virtual_map)
            aMap = virtualMap?.map?.apply {
                uiSettings.isZoomControlsEnabled = false
                uiSettings.isIndoorSwitchEnabled = false
                uiSettings.isZoomGesturesEnabled = false
                showBuildings(false)
                setRenderFps(15)
            }
            
            // 设置地图类型
            aMap?.mapType = RiderMap.instance.getDefaultMode()
            
            // 设置地图可见监听器
            virtualMap?.setActionListener(object : IActionListener {
                override fun onVisible() {
                    shadowView?.visibility = View.GONE
                }
            })
            
            // 创建地图
            virtualMap?.create(bundle)
            
            // 设置当前位置
            setupLocation()
            
            isMapInitialized = true
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Map content initialized in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize map content", e)
        }
    }
    
    /**
     * 初始化导航内容
     */
    private fun initializeNaviContent(bundle: Bundle?, display: Display) {
        if (isNaviInitialized) {
            Log.d(TAG, "Navigation content already initialized")
            return
        }
        
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Initializing navigation content at: $startTime")
            
            // 初始化导航地图
            virtualNaviMap = rootView.findViewById(R.id.navi_map)
            virtualNaviMap?.create(bundle)
            
            // 设置地图可见监听器
            virtualNaviMap?.setActionListener(object : IActionListener {
                override fun onVisible() {
                    shadowView?.visibility = View.GONE
                    setupNaviViewOptions()
                }
            })
            
            // 添加导航UI视图
            addVirtualNaviView(display)
            
            isNaviInitialized = true
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Navigation content initialized in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize navigation content", e)
        }
    }
    
    /**
     * 根据屏幕方向添加对应的导航视图
     */
    private fun addVirtualNaviView(display: Display) {
        val displayMetrics = DisplayMetrics()
        display.getMetrics(displayMetrics)
        
        virtualNaviView = if (displayMetrics.widthPixels < displayMetrics.heightPixels) {
            VirtualNaviPortView(context) // 竖屏
        } else {
            VirtualNaviLandView(context) // 横屏
        }
        
        rootView.findViewById<FrameLayout>(R.id.navi_ui_container)?.addView(virtualNaviView)
    }
    
    /**
     * 设置导航视图选项
     */
    private fun setupNaviViewOptions() {
        val mapType = RiderMap.instance.getDefaultModeNavi()
        val viewOptions = virtualNaviMap?.viewOptions?.apply {
            isSettingMenuEnabled = false
            isLayoutVisible = false
            isTrafficBarEnabled = false
            isAutoChangeZoom = false
            setModeCrossDisplayShow(false)
            isRealCrossDisplayShow = false
            isAfterRouteAutoGray = true
            isAutoLockCar = true
            isDrawBackUpOverlay = false
            lockMapDelayed = 7000L
            tilt = 35
            carBitmap = BitmapFactory.decodeResource(context.resources, R.drawable.amap_navmylocation)
            isAutoNaviViewNightMode = false
            isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
        }
        virtualNaviMap?.viewOptions = viewOptions
    }
    
    /**
     * 设置位置信息
     */
    private fun setupLocation() {
        // 先尝试使用缓存的位置
        val cachedLocation = RiderMap.instance.getLocation()
        cachedLocation?.let {
            setPosition(it.latitude, it.longitude)
        }
        
        // 异步获取最新位置
        locationJob = MainScope().launch(Dispatchers.IO) {
            try {
                val locationInfo = RiderMap.instance.getLbsLocation()
                setPosition(locationInfo.latitude, locationInfo.longitude)
                RiderMap.instance.setLocation(locationInfo)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get location", e)
            }
        }
    }

    /**
     * 设置地图位置
     */
    private fun setPosition(latitude: Double, longitude: Double) {
        if (!isMapInitialized || aMap == null) return

        val latLng = LatLng(latitude, longitude)
        aMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 17f))
        updateMarker(latLng)
    }

    /**
     * 更新位置标记
     */
    private fun updateMarker(latLng: LatLng) {
        marker?.remove()

        val markerOptions = MarkerOptions().apply {
            anchor(0.5f, 0.5f)
            isFlat = true
            visible(true)
            zIndex(2.2f)
            position(latLng)

            // 设置标记图标
            val locationBitmap = BitmapFactory.decodeResource(context.resources, R.drawable.nearme)
            icon(BitmapDescriptorFactory.fromBitmap(locationBitmap))
        }

        marker = aMap?.addMarker(markerOptions)
    }

    /**
     * 切换到地图模式
     */
    private fun switchToMap() {
        if (currentContentType == VirtualContentType.MAP) {
            Log.d(TAG, "Already in map mode")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching to map mode at: $startTime")

        currentContentType = VirtualContentType.MAP
        updateContentVisibility()

        // 恢复地图
        if (isMapInitialized) {
            virtualMap?.onResume()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Switched to map mode in ${endTime - startTime}ms")
    }

    /**
     * 切换到导航模式
     */
    private fun switchToNavigation() {
        if (currentContentType == VirtualContentType.NAVIGATION) {
            Log.d(TAG, "Already in navigation mode")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching to navigation mode at: $startTime")

        currentContentType = VirtualContentType.NAVIGATION
        updateContentVisibility()

        // 恢复导航地图
        if (isNaviInitialized) {
            virtualNaviMap?.onResume()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Switched to navigation mode in ${endTime - startTime}ms")
    }

    /**
     * 更新内容可见性
     */
    private fun updateContentVisibility() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                mapContainer?.visibility = View.VISIBLE
                naviContainer?.visibility = View.GONE
            }
            VirtualContentType.NAVIGATION -> {
                mapContainer?.visibility = View.GONE
                naviContainer?.visibility = View.VISIBLE
            }
        }
    }

    /**
     * 恢复内容
     */
    fun onResume() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                if (isMapInitialized) {
                    virtualMap?.onResume()
                }
            }
            VirtualContentType.NAVIGATION -> {
                if (isNaviInitialized) {
                    virtualNaviMap?.onResume()
                }
            }
        }
    }

    /**
     * 更改地图类型
     */
    fun changeMapType(mapType: Int) {
        // 更新地图类型
        if (isMapInitialized) {
            aMap?.mapType = mapType
        }

        // 更新导航地图类型
        if (isNaviInitialized) {
            val options = virtualNaviMap?.viewOptions?.apply {
                isAutoNaviViewNightMode = false
                isNaviNight = (mapType == AMap.MAP_TYPE_NIGHT)
            }
            virtualNaviMap?.viewOptions = options
        }

        Log.d(TAG, "Map type changed to: $mapType")
    }

    /**
     * 轻量级清理
     */
    fun cleanup() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting VirtualContentManager cleanup at: $startTime")

            // 移除导航状态监听，防止内存泄漏
            val callbackStartTime = System.currentTimeMillis()
            RiderMap.instance.removeNaviDataCallback(naviDataCallback)
            Log.d(TAG, "Removed navi callback in ${System.currentTimeMillis() - callbackStartTime}ms")

            // 立即清理关键资源
            locationJob?.cancel()
            marker?.remove()
            virtualNaviView = null

            // 异步销毁地图资源
            if (!isMapDestroyed && isMapInitialized) {
                isMapDestroyed = true
                Handler(Looper.getMainLooper()).post {
                    val mapDestroyStartTime = System.currentTimeMillis()
                    try {
                        virtualMap?.destroy()
                        Log.d(TAG, "VirtualMap destroyed in ${System.currentTimeMillis() - mapDestroyStartTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual map", e)
                    }
                }
            }

            // 异步销毁导航地图资源
            if (!isNaviMapDestroyed && isNaviInitialized) {
                isNaviMapDestroyed = true
                Handler(Looper.getMainLooper()).post {
                    val naviDestroyStartTime = System.currentTimeMillis()
                    try {
                        virtualNaviMap?.destroy()
                        Log.d(TAG, "VirtualNaviMap destroyed in ${System.currentTimeMillis() - naviDestroyStartTime}ms")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to destroy virtual navi map", e)
                    }
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualContentManager cleanup completed in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup VirtualContentManager", e)
        }
    }

    /**
     * 同步销毁
     */
    fun destroy() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting VirtualContentManager destroy at: $startTime")

            // 移除导航状态监听
            RiderMap.instance.removeNaviDataCallback(naviDataCallback)

            // 同步销毁所有资源
            locationJob?.cancel()
            marker?.remove()

            if (!isMapDestroyed && isMapInitialized) {
                isMapDestroyed = true
                virtualMap?.destroy()
            }

            if (!isNaviMapDestroyed && isNaviInitialized) {
                isNaviMapDestroyed = true
                virtualNaviMap?.destroy()
            }

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualContentManager destroyed in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy VirtualContentManager", e)
        }
    }

    companion object {
        private const val TAG = "VirtualContentManager"
    }
}
