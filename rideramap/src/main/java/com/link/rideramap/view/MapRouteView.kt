package com.link.rideramap.view

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.toColorInt
import androidx.core.view.isNotEmpty
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.TextureMapView
import com.amap.api.maps.model.LatLngBounds
import com.amap.api.navi.enums.MapStyle
import com.amap.api.navi.view.RouteOverLay
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.databinding.RideramapRouteViewBinding
import com.link.rideramap.map.NaviCallback
import com.link.rideramap.map.search.domain.entity.RouteData
import com.link.rideramap.utils.AutoSizeUtils
import com.link.rideramap.virtual.IActionListener

/**
 * 地图路径视图类
 *
 * 功能包括：
 * - 地图显示和路径规划
 * - 导航功能（开始、停止、全览、锁定模式）
 * - 主题切换（日夜模式）
 * - 交通信息显示
 * - 路径选择（支持最多3条路径）
 * - 用户交互处理
 *
 * @constructor 创建地图路径视图
 * @param context 上下文
 * @param attrs 属性集
 * @param defStyle 默认样式
 */
class MapRouteView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    companion object {
        private const val TAG = "MapRouteView"

        // 地图模式常量
        private const val AUTO = 4
        private const val DAY = AMap.MAP_TYPE_NORMAL
        private const val NIGHT = AMap.MAP_TYPE_NIGHT
        private const val NAVI_DAYTIME = 0
        private const val NAVI_NIGHT = 1

        // 导航模式常量
        private const val SHOW_MODE_LOCK = 1      // 锁定模式
        private const val SHOW_MODE_OVERVIEW = 2  // 全览模式

        // 路径相关常量
        private const val MAX_ROUTE_COUNT = 3     // 最大路径数量
        private const val DEFAULT_ROUTE_INDEX = 0 // 默认选中路径索引
        private const val SELECTED_ROUTE_TRANSPARENCY = 1.0f   // 选中路径透明度
        private const val UNSELECTED_ROUTE_TRANSPARENCY = 0.5f // 未选中路径透明度
        private const val SELECTED_ROUTE_Z_INDEX = 0           // 选中路径Z轴索引
        private const val UNSELECTED_ROUTE_Z_INDEX = -1        // 未选中路径Z轴索引

        // 地图配置常量
        private const val MAP_RENDER_FPS = 15
        private const val MAP_TILT_ANGLE = 35
        private const val MAP_LOCK_DELAYED = 7000L
        private const val MAP_PADDING_HORIZONTAL = 100
        private const val MAP_PADDING_VERTICAL = 100

        // 阴影颜色常量
        private const val SHADOW_ALPHA = 0xFF
        private const val SHADOW_RED = 0XEB
        private const val SHADOW_GREEN = 0XEB
        private const val SHADOW_BLUE = 0XEB

        // 主题颜色常量
        private const val THEME_DAY_BACKGROUND = "#FFFFFF"
        private const val THEME_NIGHT_BACKGROUND = "#2A3042"
        private const val THEME_DAY_NAV_TEXT = "#5C7BD7"
        private const val THEME_NIGHT_NAV_TEXT = "#FFFFFF"
        private const val THEME_DAY_TIME_TEXT = "#191919"
        private const val THEME_NIGHT_TIME_TEXT = "#FFFFFF"

        // 路径标签常量
        private const val ROUTE_LABEL_RECOMMEND = "推荐"
        private const val ROUTE_LABEL_PLAN_2 = "方案2"
        private const val ROUTE_LABEL_PLAN_3 = "方案3"
    }

    /** 主视图绑定 */
    private val binding =
        RideramapRouteViewBinding.inflate(LayoutInflater.from(context), this, true)

    /** 导航根视图 */
    private val rootView = LayoutInflater.from(context).inflate(R.layout.navi_view, null)

    /** 地图视图 */
    private var mapView: TextureMapView? = null

    /** 高德地图实例 */
    private var aMap: AMap? = null

    /** 导航视图 */
    private var naviView: NaviPortView? = null

    /** 导航地图 */
    private lateinit var naviMap: NaviMap

    /** 阴影视图 */
    private lateinit var shadowView: View

    /** 导航容器 */
    private var naviContainer: FrameLayout? = null


    /** 当前选中的路径索引 */
    private var selectedRouteIndex = DEFAULT_ROUTE_INDEX - 1

    /** 路径覆盖物映射 */
    private val routeOverLays: HashMap<Int, RouteOverLay> = hashMapOf()

    /** 路径数据列表 */
    private val routeList = ArrayList<RouteData>()

    /** 地图类型计数 */
    private var mapTypeCount = 0

    /** 是否夜间模式 */
    private var isNightMode = false

    /** 停止导航回调 */
    private var onStopNavigation = {}

    /** 导航数据回调 */
    private val naviDataCallback = object : NaviCallback() {
        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            naviView?.onNaviDataChanged(navigationInfo)
        }

        override fun onGpsSignalWeak(isWeek: Boolean) {
            super.onGpsSignalWeak(isWeek)
            naviView?.onGpsSignalWeak(isWeek)
        }
    }

    /** 地图类型自动模式图标资源ID */
    private var mapTypeAutoImageResId: Int = 0

    /** 地图类型白天模式图标资源ID */
    private var mapTypeDayImageResId: Int = 0

    /** 地图类型夜间模式图标资源ID */
    private var mapTypeNightImageResId: Int = 0

    /** 交通信息开启状态图标资源ID */
    private var trafficOnImageResId: Int = 0

    /** 交通信息关闭状态图标资源ID */
    private var trafficOffImageResId: Int = 0

    init {
        initializeView()
        setupRouteClickListeners()
    }

    /**
     * 初始化视图组件
     */
    private fun initializeView() {
        AutoSizeUtils.updateResources(context)
        naviContainer = findViewById(R.id.navi_container)
        mapView = binding.map
        initializeMap()
    }

    /**
     * 初始化地图
     */
    private fun initializeMap() {
        aMap = mapView?.map?.apply {
            uiSettings.isZoomControlsEnabled = false
            uiSettings.isIndoorSwitchEnabled = false
            showBuildings(false)
            setRenderFps(MAP_RENDER_FPS)
            mapType = RiderMap.instance.getDefaultMode()
        }
    }

    /**
     * 设置路径点击监听器
     */
    private fun setupRouteClickListeners() {
        // 第一条路径点击监听
        binding.firstRoute.setOnClickListener {
            handleRouteClick(0)
        }

        // 第二条路径点击监听
        binding.secondRoute.setOnClickListener {
            handleRouteClick(1)
        }

        // 第三条路径点击监听
        binding.thirdRoute.setOnClickListener {
            handleRouteClick(2)
        }
    }

    /**
     * 处理路径点击事件
     * @param routeIndex 路径索引
     */
    private fun handleRouteClick(routeIndex: Int) {
        if (routeList.size <= routeIndex) {
            return
        }

        val routeId = routeList[routeIndex].routeId
        RiderMap.instance.selectRoute(routeId)

        val routeView = when (routeIndex) {
            0 -> binding.firstRoute
            1 -> binding.secondRoute
            2 -> binding.thirdRoute
            else -> return
        }

        if (routeView.isSelected) {
            return
        }

        selectRoute(routeId, routeIndex)
    }

    /**
     * 设置导航相关配置
     */
    fun setNavi() {
        initializeShadowView()
        initializeNaviMap()
        createNaviMap()
        setupNaviMapOptions()
        addNaviDataCallback()
        setupVirtualHomeView()
        addRootViewToContainer()
        setMapTypeCount()
    }

    /**
     * 初始化阴影视图
     */
    private fun initializeShadowView() {
        shadowView = rootView.findViewById(R.id.n_map_shadow)
        shadowView.setBackgroundColor(
            Color.argb(
                SHADOW_ALPHA,
                SHADOW_RED,
                SHADOW_GREEN,
                SHADOW_BLUE
            )
        )
    }

    /**
     * 初始化导航地图
     */
    private fun initializeNaviMap() {
        naviMap = rootView.findViewById(R.id.navi_map)
        naviMap.setActionListener(object : IActionListener {
            override fun onVisible() {
                shadowView.visibility = View.GONE
            }
        })
    }

    /**
     * 设置导航地图选项
     */
    private fun setupNaviMapOptions() {
        val naviMapViewOptions = naviMap.viewOptions.apply {
            isSettingMenuEnabled = false
            isLayoutVisible = false
            isRouteListButtonShow = false
            isTrafficBarEnabled = false
            isAutoChangeZoom = false
            setModeCrossDisplayShow(false)
            isRealCrossDisplayShow = false
            isAfterRouteAutoGray = true
            isAutoLockCar = true
            isDrawBackUpOverlay = false
            lockMapDelayed = MAP_LOCK_DELAYED
            tilt = MAP_TILT_ANGLE
            carBitmap = BitmapFactory.decodeResource(resources, R.drawable.amap_navmylocation)
            setEndPointBitmap(BitmapFactory.decodeResource(resources, R.drawable.location_end))
        }
        naviMap.viewOptions = naviMapViewOptions
    }

    /**
     * 添加导航数据回调
     */
    private fun addNaviDataCallback() {
        RiderMap.instance.addNaviDataCallback(naviDataCallback)
    }

    /**
     * 设置虚拟主页视图
     */
    private fun setupVirtualHomeView() {
        addVirtualHomeView(context)
    }

    /**
     * 添加根视图到容器
     */
    private fun addRootViewToContainer() {
        naviContainer?.addView(rootView)
    }

    /**
     * 创建导航地图
     */
    private fun createNaviMap() {
        naviMap.create(null)
    }

    /**
     * 设置地图类型计数
     */
    private fun setMapTypeCount() {
        val mapModeType = RiderMap.instance.getDefaultModeNavi()
        mapTypeCount = when (mapModeType) {
            DAY -> 1
            NIGHT -> 2
            AUTO -> 3
            else -> 1
        }
    }

    /**
     * 初始化导航视图
     */
    fun initView() {
        val lockModeImageView = naviView!!.findViewById<ImageView>(R.id.navi_type_lock)
        naviMap.setShowMode(SHOW_MODE_LOCK) // 锁定模式
        lockModeImageView.visibility = GONE
        AutoSizeUtils.updateResources(context)
    }

    /**
     * 添加虚拟主页视图
     * @param context 上下文
     */
    private fun addVirtualHomeView(context: Context) {
        initializeNaviPortView(context)
        setupNaviViewClickListeners()
    }

    /**
     * 初始化导航端口视图
     * @param context 上下文
     */
    private fun initializeNaviPortView(context: Context) {
        // 获取投屏显示器的宽高，确定横竖屏
        val displayMetrics = DisplayMetrics()
        display.getMetrics(displayMetrics)
        naviView = NaviPortView(context)
        rootView.findViewById<FrameLayout>(R.id.navi_root)?.addView(naviView)
    }

    /**
     * 设置导航视图点击监听器
     */
    private fun setupNaviViewClickListeners() {
        setupTrafficClickListener()
        setupNavigationModeClickListeners()
        setupMapTypeClickListener()
        setupExitNavigationClickListeners()
    }

    /**
     * 设置交通信息点击监听器
     */
    private fun setupTrafficClickListener() {
        naviView?.findViewById<ImageView>(R.id.iv_traffic)?.setOnClickListener {
            setTrafficEnabled(!naviMap.map.isTrafficEnabled)
        }
    }

    /**
     * 设置导航模式点击监听器
     */
    private fun setupNavigationModeClickListeners() {
        val overviewModeImageView = naviView?.findViewById<ImageView>(R.id.navi_type_overview)
        val lockModeImageView = naviView?.findViewById<ImageView>(R.id.navi_type_lock)

        // 全览模式点击监听
        overviewModeImageView?.setOnClickListener {
            naviMap.setShowMode(SHOW_MODE_OVERVIEW) // 全览模式
            lockModeImageView?.visibility = VISIBLE
        }

        // 锁定模式点击监听
        lockModeImageView?.setOnClickListener {
            naviMap.setShowMode(SHOW_MODE_LOCK) // 锁定模式
            lockModeImageView.visibility = GONE
        }
    }

    /**
     * 设置地图类型点击监听器
     */
    private fun setupMapTypeClickListener() {
        naviView?.findViewById<ImageView>(R.id.map_type)?.setOnClickListener {
            val mapModeType = RiderMap.instance.getDefaultModeNavi()
            Log.e(TAG, "地图类型切换: $mapModeType")
            when (mapModeType) {
                DAY -> RiderMap.instance.onMapchange(NAVI_NIGHT)
                NIGHT -> RiderMap.instance.onMapchange(NAVI_DAYTIME)
            }
        }
    }

    /**
     * 设置退出导航点击监听器
     */
    private fun setupExitNavigationClickListeners() {
        // 显示退出确认框
        naviView?.findViewById<ImageView>(R.id.exit_navi)?.setOnClickListener {
            showExitConfirmationBar()
        }

        // 确认退出导航
        naviView?.findViewById<TextView>(R.id.btn_exit_navi)?.setOnClickListener {
            confirmExitNavigation()
        }

        // 取消退出导航
        naviView?.findViewById<TextView>(R.id.btn_exit_cancel)?.setOnClickListener {
            cancelExitNavigation()
        }
    }

    /**
     * 显示退出确认栏
     */
    private fun showExitConfirmationBar() {
        naviView?.findViewById<ConstraintLayout>(R.id.button_bar_cancel)?.visibility = VISIBLE
    }

    /**
     * 确认退出导航
     */
    private fun confirmExitNavigation() {
        onStopNavigation()
        hideExitConfirmationBar()
    }

    /**
     * 取消退出导航
     */
    private fun cancelExitNavigation() {
        hideExitConfirmationBar()
    }

    /**
     * 隐藏退出确认栏
     */
    private fun hideExitConfirmationBar() {
        naviView?.findViewById<ConstraintLayout>(R.id.button_bar_cancel)?.visibility = GONE
    }

    /**
     * 选择路径
     * @param routeId 路径ID
     * @param index 路径索引
     */
    private fun selectRoute(routeId: Int, index: Int) {
        updateNewRouteSelection(index)
        updatePreviousRouteSelection()
        updateRouteOverlayAppearance(routeId)
        selectedRouteIndex = index
    }

    /**
     * 更新新选中路径的状态
     * @param index 路径索引
     */
    private fun updateNewRouteSelection(index: Int) {
        when (index) {
            0 -> binding.firstRoute.isSelected = true
            1 -> binding.secondRoute.isSelected = true
            2 -> binding.thirdRoute.isSelected = true
        }
    }

    /**
     * 更新之前选中路径的状态
     */
    private fun updatePreviousRouteSelection() {
        when (selectedRouteIndex) {
            0 -> binding.firstRoute.isSelected = false
            1 -> binding.secondRoute.isSelected = false
            2 -> binding.thirdRoute.isSelected = false
        }

        // 更新之前选中路径的覆盖物外观
        if (selectedRouteIndex >= 0 && selectedRouteIndex < routeList.size) {
            routeOverLays[routeList[selectedRouteIndex].routeId]?.apply {
                setZindex(UNSELECTED_ROUTE_Z_INDEX)
                setTransparency(UNSELECTED_ROUTE_TRANSPARENCY)
            }
        }
    }

    /**
     * 更新路径覆盖物外观
     * @param routeId 路径ID
     */
    private fun updateRouteOverlayAppearance(routeId: Int) {
        routeOverLays[routeId]?.apply {
            setZindex(SELECTED_ROUTE_Z_INDEX)
            setTransparency(SELECTED_ROUTE_TRANSPARENCY)
        }
    }

    /**
     * 设置起点点击监听器
     * @param onStartClickListener 点击回调
     */
    fun setStartListener(onStartClickListener: () -> Unit) {
        binding.tvAddressStart.setOnClickListener {
            onStartClickListener()
        }
    }

    /**
     * 设置终点点击监听器
     * @param onEndClickListener 点击回调
     */
    fun setEndListener(onEndClickListener: () -> Unit) {
        binding.tvAddressEnd.setOnClickListener {
            onEndClickListener()
        }
    }

    /**
     * 设置导航按钮点击监听器
     * @param onNavigateClickListener 点击回调
     */
    fun setNavigateButtonListener(onNavigateClickListener: () -> Unit) {
        binding.btnNavigate.setOnClickListener {
            onNavigateClickListener()
        }
    }

    /**
     * 设置停止导航按钮监听器
     * @param onStopNavigationListener 停止导航回调
     */
    fun setStopNaviButtonListener(onStopNavigationListener: () -> Unit) {
        onStopNavigation = onStopNavigationListener
    }

    /**
     * 显示退出确认栏
     */
    fun showExitBar() {
        naviView?.findViewById<ConstraintLayout>(R.id.button_bar_cancel)?.visibility = VISIBLE
    }

    /**
     * 设置后退按钮点击监听器
     * @param onBackClickListener 点击回调
     */
    fun setBackButtonListener(onBackClickListener: () -> Unit) {
        binding.llBack.setOnClickListener {
            onBackClickListener()
        }
    }

    /**
     * 设置导航按钮是否可用
     * @param isEnabled 是否可用
     */
    fun setNavigateEnable(isEnabled: Boolean) {
        binding.btnNavigate.isEnabled = isEnabled
    }

    /**
     * 开启导航动画
     */
    fun startNaviAnimation() {
        binding.coverLayout.visibility = View.VISIBLE
        initView()
    }

    /**
     * 停止导航动画
     */
    fun stopNaviAnimation() {
        binding.coverLayout.visibility = View.GONE
    }

    /**
     * 设置起点地址
     * @param address 地址文本
     */
    fun setStart(address: String?) {
        binding.tvAddressStart.text = address
    }

    /**
     * 设置终点地址
     * @param address 地址文本
     */
    fun setDestination(address: String?) {
        binding.tvAddressEnd.text = address
    }

    /**
     * 设置路径列表
     * @param routes 路径数据列表
     */
    fun setRouteList(routes: List<RouteData>) {
        updateRouteButton(routes)
        updateMapRouteView(routes)
    }

    /**
     * 更新地图路径视图
     * @param routes 路径数据列表
     */
    private fun updateMapRouteView(routes: List<RouteData>) {
        // 清除之前的路径覆盖物
        cleanupRouteOverlays()

        val routeBoundsBuilder = LatLngBounds.Builder()

        routes.forEachIndexed { index, route ->
            val routeOverLay = createRouteOverlay(route, index)
            addRoutePointsToBounds(route, routeBoundsBuilder)
            storeAndAddRouteOverlay(route.routeId, routeOverLay)
        }

        adjustMapCameraToShowAllRoutes(routeBoundsBuilder)
    }

    /**
     * 创建路径覆盖物
     * @param route 路径数据
     * @param index 路径索引
     * @return 路径覆盖物
     */
    private fun createRouteOverlay(route: RouteData, index: Int): RouteOverLay {
        return RouteOverLay(aMap, route.naviPath, context).apply {
            if (index == DEFAULT_ROUTE_INDEX) {
                setTransparency(SELECTED_ROUTE_TRANSPARENCY)
                setZindex(SELECTED_ROUTE_Z_INDEX)
            } else {
                setTransparency(UNSELECTED_ROUTE_TRANSPARENCY)
                setZindex(UNSELECTED_ROUTE_Z_INDEX)
            }
            setStartPointBitmap(
                BitmapFactory.decodeResource(
                    resources,
                    R.drawable.amap_navimylocation
                )
            )
            setEndPointBitmap(BitmapFactory.decodeResource(resources, R.drawable.location_end))
        }
    }

    /**
     * 添加路径点到边界构建器
     * @param route 路径数据
     * @param routeBoundsBuilder 边界构建器
     */
    private fun addRoutePointsToBounds(route: RouteData, routeBoundsBuilder: LatLngBounds.Builder) {
        route.points.takeIf { isNotEmpty() }?.forEach { latLng ->
            routeBoundsBuilder.include(latLng)
        }
    }

    /**
     * 存储并添加路径覆盖物到地图
     * @param routeId 路径ID
     * @param routeOverLay 路径覆盖物
     */
    private fun storeAndAddRouteOverlay(routeId: Int, routeOverLay: RouteOverLay) {
        routeOverLays[routeId] = routeOverLay
        routeOverLay.addToMap()
    }

    /**
     * 调整地图相机以显示所有路径
     * @param routeBoundsBuilder 边界构建器
     */
    private fun adjustMapCameraToShowAllRoutes(routeBoundsBuilder: LatLngBounds.Builder) {
        routeBoundsBuilder.build()?.let { bounds ->
            val mapPaddingTop = binding.linearLayoutCompat.height
            val mapPaddingBottom = binding.routeBottomBox.height
            Log.d(TAG, "调整地图视角以显示所有路径")
            aMap?.moveCamera(
                CameraUpdateFactory.newLatLngBoundsRect(
                    bounds,
                    MAP_PADDING_HORIZONTAL,
                    MAP_PADDING_VERTICAL,
                    mapPaddingTop,
                    mapPaddingBottom
                )
            )
        }
    }

    /**
     * 更新路径按钮
     * @param routes 路径数据列表
     */
    private fun updateRouteButton(routes: List<RouteData>) {
        clearAndUpdateRouteList(routes)
        updateRouteButtonsData(routes)
    }

    /**
     * 清空并更新路径列表
     * @param routes 路径数据列表
     */
    private fun clearAndUpdateRouteList(routes: List<RouteData>) {
        routeList.clear()
        routeList.addAll(routes)
    }

    /**
     * 更新路径按钮数据
     * @param routes 路径数据列表
     */
    private fun updateRouteButtonsData(routes: List<RouteData>) {
        routes.forEachIndexed { index, routeData ->
            if (index >= MAX_ROUTE_COUNT) {
                return@forEachIndexed
            }
            updateSingleRouteButton(index, routeData)
        }
    }

    /**
     * 更新单个路径按钮
     * @param index 路径索引
     * @param routeData 路径数据
     */
    private fun updateSingleRouteButton(index: Int, routeData: RouteData) {
        when (index) {
            0 -> {
                selectedRouteIndex = DEFAULT_ROUTE_INDEX
                binding.firstRoute.setData(ROUTE_LABEL_RECOMMEND, routeData, isNightMode)
                binding.firstRoute.isSelected = true
            }

            1 -> {
                binding.secondRoute.visibility = View.VISIBLE
                binding.secondRoute.setData(ROUTE_LABEL_PLAN_2, routeData, isNightMode)
            }

            2 -> {
                binding.thirdRoute.visibility = View.VISIBLE
                binding.thirdRoute.setData(ROUTE_LABEL_PLAN_3, routeData, isNightMode)
            }
        }
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        mapView?.onCreate(null)
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == View.VISIBLE) {
            mapView?.onResume()
        } else {
            mapView?.onPause()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mapView?.onResume()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cleanupRouteOverlays()
        mapView?.onDestroy()
    }

    /**
     * 清理路径覆盖物
     */
    private fun cleanupRouteOverlays() {
        routeOverLays.forEach { (_, overlay) ->
            overlay.removeFromMap()
            overlay.destroy()
        }
        routeOverLays.clear()
    }

    /**
     * 设置交通信息是否开启
     * @param isTrafficEnabled 是否开启交通信息
     */
    private fun setTrafficEnabled(isTrafficEnabled: Boolean) {
        naviMap.map?.isTrafficEnabled = isTrafficEnabled
        updateTrafficIcon(isTrafficEnabled)
    }

    /**
     * 更新交通信息图标
     * @param isTrafficEnabled 是否开启交通信息
     */
    private fun updateTrafficIcon(isTrafficEnabled: Boolean) {
        val trafficImageView = naviView?.findViewById<ImageView>(R.id.iv_traffic)
        val iconResId = if (isTrafficEnabled) trafficOnImageResId else trafficOffImageResId
        trafficImageView?.setImageResource(iconResId)
    }

    /**
     * 切换地图类型
     */
    fun changeMap() {
        val mapTypeImageView = findViewById<ImageView>(R.id.map_type)
        val realType = RiderMap.instance.getDefaultModeNavi()

        updateMapType(realType)
        updateNaviMapStyle(realType)
        updateMapTypeIcon(mapTypeImageView, realType)
    }

    /**
     * 更新地图类型
     * @param mapType 地图类型
     */
    private fun updateMapType(mapType: Int) {
        aMap?.mapType = RiderMap.instance.getDefaultMode()
    }

    /**
     * 更新导航地图样式
     * @param mapType 地图类型
     */
    private fun updateNaviMapStyle(mapType: Int) {
        val naviMapViewOptions = naviMap.viewOptions.apply {
            if (mapType == AMap.MAP_TYPE_NIGHT) {
                setMapStyle(MapStyle.NIGHT, null)
            } else {
                setMapStyle(MapStyle.DAY, null)
            }
        }
        naviMap.viewOptions = naviMapViewOptions
    }

    /**
     * 更新地图类型图标
     * @param mapTypeImageView 地图类型图标视图
     * @param mapType 地图类型
     */
    private fun updateMapTypeIcon(mapTypeImageView: ImageView, mapType: Int) {
        val iconResId = when (mapType) {
            DAY -> mapTypeDayImageResId
            NIGHT -> mapTypeNightImageResId
            else -> mapTypeDayImageResId
        }
        mapTypeImageView.setImageResource(iconResId)
    }

    /**
     * 初始化主题
     * @param themeColorProvider 主题颜色提供者
     * @param themeStringProvider 主题字符串提供者
     * @param isNightModeProvider 夜间模式判断提供者
     */
    fun initTheme(
        themeColorProvider: (resId: Int) -> Int,
        themeStringProvider: (day: String, night: String) -> String,
        isNightModeProvider: () -> Boolean
    ) {
        Log.e(TAG, "初始化主题: ${isNightModeProvider()}")

        applyRouteViewTheme(themeColorProvider, themeStringProvider)
        applyRouteButtonTheme(isNightModeProvider)
        updateNightModeState(isNightModeProvider)
        applyNavigationViewTheme(themeColorProvider, themeStringProvider)
        changeMap()
    }

    /**
     * 应用路径视图主题
     * @param themeColorProvider 主题颜色提供者
     * @param themeStringProvider 主题字符串提供者
     */
    private fun applyRouteViewTheme(
        themeColorProvider: (resId: Int) -> Int,
        themeStringProvider: (day: String, night: String) -> String
    ) {
        binding.linearLayoutCompat.setBackgroundColor(
            themeStringProvider(THEME_DAY_BACKGROUND, THEME_NIGHT_BACKGROUND).toColorInt()
        )
        binding.btnNavigate.setTextColor(
            themeStringProvider(THEME_DAY_NAV_TEXT, THEME_NIGHT_NAV_TEXT).toColorInt()
        )
        binding.btnNavigate.setBackgroundResource(themeColorProvider(R.drawable.bt_map_navi_background))
        binding.seBackground.setBackgroundResource(themeColorProvider(R.drawable.start_end_background))
        binding.routeBottomBox.setBackgroundResource(themeColorProvider(R.drawable.route_select_background))
        binding.ibBack.setBackgroundResource(themeColorProvider(R.drawable.btn_map_back))
    }

    /**
     * 应用路径按钮主题
     * @param isNightModeProvider 夜间模式判断提供者
     */
    private fun applyRouteButtonTheme(isNightModeProvider: () -> Boolean) {
        val isNightMode = isNightModeProvider()
        binding.firstRoute.onThemeChange(isNightMode)
        binding.secondRoute.onThemeChange(isNightMode)
        binding.thirdRoute.onThemeChange(isNightMode)
    }

    /**
     * 更新夜间模式状态
     * @param isNightModeProvider 夜间模式判断提供者
     */
    private fun updateNightModeState(isNightModeProvider: () -> Boolean) {
        this.isNightMode = isNightModeProvider()
    }

    /**
     * 应用导航视图主题
     * @param themeColorProvider 主题颜色提供者
     * @param themeStringProvider 主题字符串提供者
     */
    private fun applyNavigationViewTheme(
        themeColorProvider: (resId: Int) -> Int,
        themeStringProvider: (day: String, night: String) -> String
    ) {
        val naviViews = getNavigationViewReferences()
        applyNavigationViewStyles(naviViews, themeColorProvider)
        storeThemeResourceIds(themeColorProvider)
        applyNavigationTextTheme(naviViews.naviTimeTextView, themeStringProvider)
    }

    /**
     * 获取导航视图引用
     * @return 导航视图引用对象
     */
    private fun getNavigationViewReferences(): NavigationViewReferences {
        return NavigationViewReferences(
            rightBarBackgroundImageView = naviView?.findViewById(R.id.navi_right_bar),
            overviewModeImageView = naviView?.findViewById(R.id.navi_type_overview),
            trafficImageView = naviView?.findViewById(R.id.iv_traffic),
            mapTypeModeImageView = naviView?.findViewById(R.id.map_type),
            lockModeImageView = naviView?.findViewById(R.id.navi_type_lock),
            bottomBarBackgroundLayout = naviView?.findViewById(R.id.button_bar),
            exitNaviImageView = naviView?.findViewById(R.id.exit_navi),
            naviTimeTextView = naviView?.findViewById(R.id.tv_navi_time)
        )
    }

    /**
     * 应用导航视图样式
     * @param naviViews 导航视图引用
     * @param themeColorProvider 主题颜色提供者
     */
    private fun applyNavigationViewStyles(
        naviViews: NavigationViewReferences,
        themeColorProvider: (resId: Int) -> Int
    ) {
        naviViews.apply {
            rightBarBackgroundImageView?.setImageResource(themeColorProvider(R.drawable.navi_right_bar_background))
            overviewModeImageView?.setImageResource(themeColorProvider(R.drawable.rb_overview_navi))
            trafficImageView?.setImageResource(themeColorProvider(R.drawable.road_condition_on))
            lockModeImageView?.setImageResource(themeColorProvider(R.drawable.rb_lock_navi))
            bottomBarBackgroundLayout?.setBackgroundResource(themeColorProvider(R.drawable.navi_bar_background))
            exitNaviImageView?.setImageResource(themeColorProvider(R.drawable.cancel_navi))
        }
    }

    /**
     * 存储主题资源ID
     * @param themeColorProvider 主题颜色提供者
     */
    private fun storeThemeResourceIds(themeColorProvider: (resId: Int) -> Int) {
        trafficOnImageResId = themeColorProvider(R.drawable.road_condition_on)
        trafficOffImageResId = themeColorProvider(R.drawable.road_condition_off)
        mapTypeAutoImageResId = themeColorProvider(R.drawable.rb_auto_type)
        mapTypeDayImageResId = themeColorProvider(R.drawable.rb_day_type)
        mapTypeNightImageResId = themeColorProvider(R.drawable.rb_night_type)
    }

    /**
     * 应用导航文本主题
     * @param naviTimeTextView 导航时间文本视图
     * @param themeStringProvider 主题字符串提供者
     */
    private fun applyNavigationTextTheme(
        naviTimeTextView: TextView?,
        themeStringProvider: (day: String, night: String) -> String
    ) {
        naviTimeTextView?.setTextColor(
            themeStringProvider(THEME_DAY_TIME_TEXT, THEME_NIGHT_TIME_TEXT).toColorInt()
        )
    }

    /**
     * 导航视图引用数据类
     * 用于组织导航相关的视图引用，便于主题应用
     */
    private data class NavigationViewReferences(
        val rightBarBackgroundImageView: ImageView?,
        val overviewModeImageView: ImageView?,
        val trafficImageView: ImageView?,
        val mapTypeModeImageView: ImageView?,
        val lockModeImageView: ImageView?,
        val bottomBarBackgroundLayout: ConstraintLayout?,
        val exitNaviImageView: ImageView?,
        val naviTimeTextView: TextView?
    )
}