package com.link.rideramap.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.CustomRenderer
import com.amap.api.maps.TextureMapView
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.link.rideramap.R
import com.link.rideramap.api.RiderMap
import com.link.rideramap.databinding.SimpleMapviewBinding
import com.link.rideramap.utils.AutoSizeUtils
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

/**
 * 简单的地图视图组件
 * 
 * 提供以下核心功能：
 * - 地图显示和基础交互
 * - 定位标记的显示和管理
 * - 搜索标记的添加和移除
 * - 交通状况的开关控制
 * - 用户交互事件的监听
 * 
 * <AUTHOR> Team
 */
class SimpleMapview @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    // ========================= 视图绑定 =========================
    private val binding = SimpleMapviewBinding.inflate(LayoutInflater.from(context), this, true)
    
    // ========================= 地图相关组件 =========================
    /** 高德地图实例 */
    private lateinit var mAMap: AMap
    /** 地图视图 */
    private var mapView: TextureMapView = binding.map
    
    // ========================= 标记管理 =========================
    /** 当前位置标记 */
    private var mMarker: Marker? = null
    /** 搜索/点击标记 */
    private var mClickMarker: Marker? = null

    // ========================= 初始化 =========================
    init {
        initializeView()
        initializeMap()
        setupEventListeners()
    }

    /**
     * 初始化视图组件
     */
    private fun initializeView() {
        AutoSizeUtils.updateResources(context)
        // 设置地图阴影背景色
        binding.vMapShadow.setBackgroundColor(
            Color.argb(
                MAP_SHADOW_ALPHA,
                MAP_SHADOW_COLOR_R,
                MAP_SHADOW_COLOR_G,
                MAP_SHADOW_COLOR_B
            )
        )
    }

    /**
     * 初始化地图配置
     */
    private fun initializeMap() {
        mAMap = mapView.map.apply {
            // 启用交通状况显示
            isTrafficEnabled = true
            // 设置自定义渲染器
            setCustomRenderer(createCustomRenderer())
            // 设置默认地图模式
            mapType = RiderMap.instance.getDefaultMode()
        }
        
        // 在 mAMap 赋值完成后配置地图设置
        configureMapUI()
        configureMapSettings()
    }

    /**
     * 配置地图UI设置
     */
    private fun configureMapUI() {
        mAMap.uiSettings.apply {
            isZoomControlsEnabled = false
            isIndoorSwitchEnabled = false
        }
    }

    /**
     * 配置地图基础设置
     */
    private fun configureMapSettings() {
        mAMap.apply {
            showBuildings(false)
            setRenderFps(MAP_RENDER_FPS)
            moveCamera(CameraUpdateFactory.zoomTo(DEFAULT_ZOOM_LEVEL))
        }
    }

    /**
     * 设置事件监听器
     */
    private fun setupEventListeners() {
        // 交通状况按钮点击事件
        binding.ivTraffic.setOnClickListener {
            setTrafficEnabled(!mAMap.isTrafficEnabled)
        }

        // 地图点击事件 - 清除搜索标记和地址名称
        mAMap.setOnMapClickListener { latLng ->
            latLng?.let {
                removeMarker()
                setSearchName("")
            }
        }
    }

    /**
     * 创建自定义渲染器
     * 用于地图渲染完成后隐藏阴影视图
     */
    private fun createCustomRenderer(): CustomRenderer {
        return object : CustomRenderer {
            override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
                // 移除自定义渲染器，避免重复调用
                mAMap.setCustomRenderer(null)
                // 在主线程中隐藏阴影视图
                mapView.post {
                    binding.vMapShadow.visibility = View.GONE
                }
            }

            override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
                // 地图表面尺寸变化时的处理
            }

            override fun onDrawFrame(gl: GL10?) {
                // 地图绘制帧时的处理
            }

            override fun OnMapReferencechanged() {
                // 地图引用变化时的处理
            }
        }
    }

    // ========================= 生命周期管理 =========================

    override fun onFinishInflate() {
        super.onFinishInflate()
        mapView.onCreate(null)
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == View.VISIBLE) {
            mapView.onResume()
        } else {
            mapView.onPause()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mapView.onResume()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 清理资源
        cleanupMarkers()
        mapView.onDestroy()
    }

    /**
     * 清理所有标记资源
     */
    private fun cleanupMarkers() {
        mMarker?.remove()
        mClickMarker?.remove()
    }

    // ========================= 标记管理方法 =========================

    /**
     * 添加搜索标记
     * @param latitude 纬度
     * @param longitude 经度
     * @param bitmap 标记图标
     */
    fun addMarker(latitude: Double, longitude: Double, bitmap: Bitmap) {
        // 移除已存在的搜索标记
        mClickMarker?.remove()
        
        val latLng = LatLng(latitude, longitude)
        mClickMarker = mAMap.addMarker(
            MarkerOptions()
                .icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                .anchor(SEARCH_MARKER_ANCHOR_X, SEARCH_MARKER_ANCHOR_Y)
                .position(latLng)
                .zIndex(SEARCH_MARKER_Z_INDEX)
                .visible(true)
        )
        
        // 更新搜索按钮图标为导航图标
        binding.ibSearch.setBackgroundResource(R.drawable.btn_map_navroute)
    }

    /**
     * 移除搜索标记
     */
    fun removeMarker() {
        mClickMarker?.remove()
        // 恢复搜索按钮图标
        binding.ibSearch.setBackgroundResource(R.drawable.btn_map_search)
    }

    /**
     * 设置当前位置并显示定位标记
     * @param latitude 纬度
     * @param longitude 经度
     */
    fun setPosition(latitude: Double, longitude: Double) {
        val latLng = LatLng(latitude, longitude)
        // 移动地图中心到指定位置
        mAMap.moveCamera(CameraUpdateFactory.newLatLng(latLng))
        // 更新定位标记
        updateLocationMarker(latLng)
    }

    /**
     * 更新定位标记
     * @param latLng 位置坐标
     */
    private fun updateLocationMarker(latLng: LatLng) {
        // 移除旧的定位标记
        mMarker?.remove()
        
        val markerOptions = MarkerOptions().apply {
            anchor(LOCATION_MARKER_ANCHOR_X, LOCATION_MARKER_ANCHOR_Y)
            isFlat = true
            visible(true)
            zIndex(LOCATION_MARKER_Z_INDEX)
            icon(BitmapDescriptorFactory.fromResource(R.drawable.nearme))
            position(latLng)
        }
        
        mMarker = mAMap.addMarker(markerOptions)
    }

    // ========================= 地址名称管理 =========================

    /**
     * 设置搜索地址名称
     * @param addressName 地址名称
     */
    fun setSearchName(addressName: String) {
        binding.tvAddressName.text = addressName
    }

    /**
     * 获取当前搜索地址名称
     * @return 地址名称
     */
    fun getSearchName(): CharSequence {
        return binding.tvAddressName.text
    }

    // ========================= 事件监听器设置 =========================

    /**
     * 设置地图长按事件监听器
     * @param block 长按事件回调，参数为经纬度
     */
    fun setOnMapLongClickListener(block: (latitude: Double, longitude: Double) -> Unit) {
        mAMap.setOnMapLongClickListener { latLng ->
            block(latLng.latitude, latLng.longitude)
        }
    }

    /**
     * 设置定位按钮点击事件监听器
     * @param block 点击事件回调
     */
    fun setLocateClickListener(block: () -> Unit) {
        binding.ivLocate.setOnClickListener {
            block()
        }
    }

    /**
     * 设置搜索按钮点击事件监听器
     * @param block 点击事件回调
     */
    fun setSearchClickListener(block: () -> Unit) {
        binding.ibSearch.setOnClickListener {
            block()
        }
        binding.tvAddressName.setOnClickListener {
            block()
        }
    }

    // ========================= 地图配置方法 =========================

    /**
     * 切换地图模式
     */
    fun changeMap() {
        mAMap.mapType = RiderMap.instance.getDefaultMode()
    }

    /**
     * 设置交通状况显示状态
     * @param isTrafficEnabled 是否显示交通状况
     */
    private fun setTrafficEnabled(isTrafficEnabled: Boolean) {
        mAMap.isTrafficEnabled = isTrafficEnabled
        
        // 更新交通状况按钮图标
        val iconResource = if (isTrafficEnabled) {
            R.drawable.btn_road_condition_on
        } else {
            R.drawable.btn_road_condition_off
        }
        binding.ivTraffic.setImageResource(iconResource)
    }

    // ========================= 常量定义 =========================
    companion object {
        private const val TAG = "SimpleMapview"
        
        // 地图阴影配置
        private const val MAP_SHADOW_ALPHA = 0xFF
        private const val MAP_SHADOW_COLOR_R = 0XEB
        private const val MAP_SHADOW_COLOR_G = 0XEB
        private const val MAP_SHADOW_COLOR_B = 0XEB
        
        // 地图基础配置
        private const val MAP_RENDER_FPS = 15
        private const val DEFAULT_ZOOM_LEVEL = 15f
        
        // 标记配置 - 搜索标记
        private const val SEARCH_MARKER_ANCHOR_X = 0.5f
        private const val SEARCH_MARKER_ANCHOR_Y = 0.85f
        private const val SEARCH_MARKER_Z_INDEX = 1.6f
        
        // 标记配置 - 定位标记
        private const val LOCATION_MARKER_ANCHOR_X = 0.5f
        private const val LOCATION_MARKER_ANCHOR_Y = 0.5f
        private const val LOCATION_MARKER_Z_INDEX = 2.2f
    }
}