<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout4"
        android:layout_width="0dp"
        android:layout_height="83dp"
        android:background="#040507"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_navi_icon"
            android:layout_width="51dp"
            android:layout_height="51dp"
            android:layout_marginStart="27dp"
            android:layout_marginTop="7dp"
            android:src="@drawable/amap_left"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_next_road_distance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#5C7BD7"
            android:layout_marginStart="20dp"
            android:text="200m"
            android:textSize="34sp"
            android:textStyle="bold"
            android:gravity="bottom"
            android:paddingBottom="0dp"
            android:includeFontPadding="false"
            app:layout_constraintLeft_toRightOf="@id/iv_navi_icon"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_direction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:layout_marginStart="20dp"
            android:text="左转"
            android:lines="1"
            android:textColor="#5C7BD7"
            android:textSize="22sp"
            app:layout_constraintLeft_toRightOf="@id/iv_navi_icon"
            app:layout_constraintTop_toBottomOf="@+id/tv_next_road_distance" />


        <TextView
            android:id="@+id/tv_next_road"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:text="进入北星大道二段38巷"
            android:textColor="#FFFFFF"
            android:textSize="22sp"
            android:layout_marginStart="8dp"
            app:layout_constraintLeft_toRightOf="@id/tv_direction"
            app:layout_constraintTop_toBottomOf="@+id/tv_next_road_distance" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_gps_signal"
        android:layout_width="106dp"
        android:layout_height="25dp"
        android:background="@drawable/amap_gpdlow_v"
        android:gravity="center_vertical"
        android:paddingStart="23dp"
        android:paddingEnd="0dp"
        android:textColor="#37F0FF"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout4" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_bar"
        android:layout_width="match_parent"
        android:layout_height="78dp"
        android:background="@drawable/navi_bar_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/navi_detail_box"
            android:layout_width="wrap_content"
            android:layout_height="52dp"
            android:layout_marginTop="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:id="@+id/tv_navi_distance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="10.3公里"
                android:textColor="#5C7BD7"
                android:textSize="19sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <TextView
                android:id="@+id/tv_navi_remaintime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="20分钟"
                android:layout_marginStart="6dp"
                android:textColor="#5C7BD7"
                android:textSize="19sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/tv_navi_distance"
                app:layout_constraintTop_toTopOf="parent" />
            <ImageView
                android:id="@+id/img_navi_traffic"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="3.5dp"
                android:layout_marginStart="6dp"
                android:src="@drawable/traffic_light_num"
                app:layout_constraintLeft_toRightOf="@+id/tv_navi_remaintime"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_navi_traffic"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="8"
                android:textColor="#5C7BD7"
                android:textSize="19sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@+id/img_navi_traffic"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tv_navi_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="预计下午2点46分到达"
                android:textColor="#191919"
                android:textSize="15sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_bar_cancel"
        android:layout_width="match_parent"
        android:layout_height="78dp"
        android:visibility="gone"
        android:background="@drawable/navi_bar_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/navi_cancel_select"
            android:layout_width="271dp"
            android:layout_height="31dp"
            android:layout_marginTop="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:id="@+id/btn_exit_navi"
                android:layout_width="120dp"
                android:layout_height="31dp"
                android:text="退出导航"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="#F6685D"
                android:gravity="center_horizontal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>
            <TextView
                android:id="@+id/tv_separated"
                android:layout_width="120dp"
                android:layout_height="31dp"
                android:text="|"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="#7C7C7C"
                android:gravity="center_horizontal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>
            <TextView
                android:id="@+id/btn_exit_cancel"
                android:layout_width="120dp"
                android:layout_height="31dp"
                android:text="取消"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="#7C7C7C"
                android:gravity="center_horizontal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
