<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout4"
        android:layout_width="0dp"
        android:layout_height="83dp"
        android:background="#040507"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_navi_icon"
            android:layout_width="51dp"
            android:layout_height="51dp"
            android:layout_marginStart="27dp"
            android:layout_marginTop="7dp"
            android:src="@drawable/amap_left"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_next_road_distance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#5C7BD7"
            android:layout_marginStart="20dp"
            android:text="200m"
            android:textSize="34sp"
            android:textStyle="bold"
            android:gravity="bottom"
            android:paddingBottom="0dp"
            android:includeFontPadding="false"
            app:layout_constraintLeft_toRightOf="@id/iv_navi_icon"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_direction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:layout_marginStart="20dp"
            android:text="左转"
            android:lines="1"
            android:textColor="#5C7BD7"
            android:textSize="22sp"
            app:layout_constraintLeft_toRightOf="@id/iv_navi_icon"
            app:layout_constraintTop_toBottomOf="@+id/tv_next_road_distance" />


        <TextView
            android:id="@+id/tv_next_road"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:text="进入北星大道二段38巷"
            android:textColor="#FFFFFF"
            android:textSize="22sp"
            android:layout_marginStart="8dp"
            app:layout_constraintLeft_toRightOf="@id/tv_direction"
            app:layout_constraintTop_toBottomOf="@+id/tv_next_road_distance" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_gps_signal"
        android:layout_width="106dp"
        android:layout_height="25dp"
        android:background="@drawable/amap_gpdlow_v"
        android:gravity="center_vertical"
        android:paddingStart="23dp"
        android:paddingEnd="0dp"
        android:textColor="#37F0FF"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout4" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_bar"
        android:layout_width="match_parent"
        android:layout_height="78dp"
        android:background="@drawable/navi_bar_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/exit_navi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/cancel_navi"
            android:layout_marginStart="29dp"
            android:layout_marginTop="17dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/navi_detail_box"
            android:layout_width="wrap_content"
            android:layout_height="52dp"
            android:layout_marginTop="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:id="@+id/tv_navi_distance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="10.3公里"
                android:textColor="#5C7BD7"
                android:textSize="19sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <TextView
                android:id="@+id/tv_navi_remaintime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="20分钟"
                android:layout_marginStart="6dp"
                android:textColor="#5C7BD7"
                android:textSize="19sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/tv_navi_distance"
                app:layout_constraintTop_toTopOf="parent" />
            <ImageView
                android:id="@+id/img_navi_traffic"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="3.5dp"
                android:layout_marginStart="6dp"
                android:src="@drawable/traffic_light_num"
                app:layout_constraintLeft_toRightOf="@+id/tv_navi_remaintime"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_navi_traffic"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="8"
                android:textColor="#5C7BD7"
                android:textSize="19sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@+id/img_navi_traffic"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tv_navi_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="预计下午2点46分到达"
                android:textColor="#191919"
                android:textSize="15sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_bar_cancel"
        android:layout_width="match_parent"
        android:layout_height="78dp"
        android:visibility="gone"
        android:background="@drawable/navi_bar_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/navi_cancel_select"
            android:layout_width="271dp"
            android:layout_height="31dp"
            android:layout_marginTop="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:id="@+id/btn_exit_navi"
                android:layout_width="120dp"
                android:layout_height="31dp"
                android:text="退出导航"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="#F6685D"
                android:gravity="center_horizontal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>
            <TextView
                android:id="@+id/tv_separated"
                android:layout_width="120dp"
                android:layout_height="31dp"
                android:text="|"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="#7C7C7C"
                android:gravity="center_horizontal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>
            <TextView
                android:id="@+id/btn_exit_cancel"
                android:layout_width="120dp"
                android:layout_height="31dp"
                android:text="取消"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="#7C7C7C"
                android:gravity="center_horizontal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
    <ImageView
        android:id="@+id/navi_right_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="11.19dp"
        android:layout_marginEnd="10dp"
        android:src="@drawable/navi_right_bar_background"
        app:layout_constraintBottom_toTopOf="@+id/button_bar"
        app:layout_constraintRight_toRightOf="parent"/>
    <ImageView
        android:id="@+id/iv_traffic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:src="@drawable/road_condition_on"
        app:layout_constraintTop_toTopOf="@+id/navi_right_bar"
        app:layout_constraintLeft_toLeftOf="@+id/navi_right_bar"
        app:layout_constraintRight_toRightOf="@+id/navi_right_bar"/>
    <ImageView
        android:id="@+id/map_type"
        android:src="@drawable/rb_auto_type"
        android:layout_width="24dp"
        android:layout_height="42dp"
        android:layout_marginTop="16dp"
        app:layout_constraintLeft_toLeftOf="@+id/navi_right_bar"
        app:layout_constraintRight_toRightOf="@+id/navi_right_bar"
        app:layout_constraintTop_toBottomOf="@+id/iv_traffic" />
    <ImageView
        android:id="@+id/navi_type_overview"
        android:src="@drawable/rb_overview_navi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintLeft_toLeftOf="@+id/navi_right_bar"
        app:layout_constraintRight_toRightOf="@+id/navi_right_bar"
        app:layout_constraintTop_toBottomOf="@+id/map_type" />
    <ImageView
        android:id="@+id/navi_type_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/rb_lock_navi"
        android:layout_marginEnd="4dp"
        android:visibility="gone"
        app:layout_constraintRight_toLeftOf="@+id/navi_right_bar"
        app:layout_constraintBottom_toBottomOf="@+id/navi_right_bar"/>
</androidx.constraintlayout.widget.ConstraintLayout>
